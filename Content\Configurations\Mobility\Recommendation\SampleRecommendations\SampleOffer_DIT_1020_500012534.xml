<?xml version="1.0" encoding="utf-8"?>
<SampleEsbOffers>
	<SampleEsbOffer>
		<OfferCode>1020</OfferCode>
		<EffectiveDate>2020-01-01</EffectiveDate>
		<EndDate>2020-12-01</EndDate>
		<Priority>102</Priority>
		<Zones>		
            <Zone>SS_LANDING_PAGES</Zone>
            <Zone>SS_PLAN_CHANGE</Zone>
            <Zone>SS_HUG</Zone>
            <Zone>SS_ADD-ONS_NON-DATA_NON-TRAVEL</Zone>
            <Zone>SS_DATA</Zone>
            <Zone>SS_TRAVEL</Zone>
            <Zone>SS_ADD_MOB_LINE</Zone>
            <Zone>SS_ADD_BRS</Zone>

		</Zones>
		<Subscribers>		
            <Subscriber id="**********" mdn="**********"/>
            <Subscriber id="**********" mdn="**********"/>
            <Subscriber id="**********" mdn="**********"/>
		
		</Subscribers>
		<AccountNumber>*********</AccountNumber>
        <Socs>
            <Soc>
                <Code>VNUMCGH24</Code>                
                <Type>P</Type>
                <Level>C</Level>
                <ConditionalSocIndicator>N</ConditionalSocIndicator>
                <EffectiveDate>2020-01-01T17:40:42.243Z</EffectiveDate>
                <ExpiredDate>2020-12-21T17:40:42.243Z</ExpiredDate>
            </Soc>
                    </Socs>
	</SampleEsbOffer>
</SampleEsbOffers>
