﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="settings">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="applicationSettings">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="key">
                <xs:complexType>
                  <xs:simpleContent>
                    <xs:extension base="xs:string">
                      <xs:attribute name="name" type="xs:string" use="required" />
                    </xs:extension>
                  </xs:simpleContent>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
            <xs:attribute name="targetHostName" type="xs:string" />
          </xs:complexType>
        </xs:element>
        <xs:element name="flowSettings">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="key">
                <xs:complexType>
                  <xs:attribute name="name" type="xs:string" use="required" />
                </xs:complexType>
              </xs:element>
            </xs:sequence>
            <xs:attribute name="targetHostName" type="xs:string" />
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>