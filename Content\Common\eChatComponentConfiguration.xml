﻿<?xml version="1.0" encoding="utf-8"?>
<echat>
	<typing mode="ON">
		<delay>2000</delay>
		<image>
			<Image_en>
				<ImageURL>step-tracker_loop.gif</ImageURL>
				<ImageALT>Agent is typing Icon</ImageALT>
			</Image_en>
			<Image_fr>
				<ImageURL>step-tracker_loop.gif</ImageURL>
				<ImageALT>L'agent écrit Icône</ImageALT>
			</Image_fr>
		</image>
	</typing>
	<!--
	 <Operational mode = “OFF”> = chat functionality is operational 24*7
	 <Operational mode = “ON”> AND <DATE AND TIME DEFINED> = operational only during the time interval mentioned during that particular day.
	 <Operational mode = “ON”> AND <DATE AND TIME NOT DEFINED> = No chat during that particular day.
	-->
	<operational mode="ON">
		<hours day="monday">
			<from>00:00</from>
			<to>23:59</to>
		</hours>
		<hours day="tuesday">
			<from>00:00</from>
			<to>23:59</to>
		</hours>
		<hours day="wednesday">
			<from>00:00</from>
			<to>23:59</to>
		</hours>
		<hours day="Thursday">
			<from>00:00</from>
			<to>23:59</to>
		</hours>
		<hours day="friday">
			<from>00:00</from>
			<to>23:59</to>
		</hours>
		<hours day="saturday">
			<from>00:00</from>
			<to>23:59</to>
		</hours>
		<hours day="sunday">
			<from>00:00</from>
			<to>23:59</to>
		</hours>
	</operational>
	<!-- Anchor button visibility - Add all those pages where anchor button need not to be shown. -->
	<anchor>
		<visibility>
			<exclude>
				<pageid province="AB,BC,MB,NB,NL,NT,NS,NU,ON,PE,QC,SK,YT">B_MYB_EN_VirtualRepair</pageid>
				<pageid province="AB,BC,MB,NB,NL,NT,NS,NU,ON,PE,QC,SK,YT">B_MYB_EN_VirtualRepairFMO</pageid>
				<pageid province="AB,BC,MB,NB,NL,NT,NS,NU,ON,PE,QC,SK,YT">B_MYB_FR_VirtualRepairFMO</pageid>
				<pageid province="AB,BC,MB,NB,NL,NT,NS,NU,ON,PE,QC,SK,YT">B_MYB_FR_ReparationVirtuelle</pageid>
			</exclude>
		</visibility>
	</anchor>
	<!-- Proactive chat enabled pages - Add all those pages where proactive chat messages that needs to be shown. -->
	<proactive>
		<include>
			<pageid province="AB|BC|MB|NB|NL|NT|NS|NU|ON|PE|QC|SK|YT">B_MYB_EN_Mobility_Prepaid_OnetimeTopup</pageid>
			<pageid province="AB|BC|MB|NB|NL|NT|NS|NU|ON|PE|QC|SK|YT">B_MYB_FR_Mobility_Prepaid_OnetimeTopup</pageid>
			<pageid province="AB|BC|MB|NB|NL|NT|NS|NU|ON|PE|QC|SK|YT">B_MYB_EN_Mobility_Prepaid_PreauthorizedTopups</pageid>
			<pageid province="AB|BC|MB|NB|NL|NT|NS|NU|ON|PE|QC|SK|YT">B_MYB_FR_Mobility_Prepaid_PreauthorizedTopups</pageid>
		</include>
	</proactive>
	<categories>
		<category name="eCare HUG" proactiveTimerInSec="120000">
		</category>
		<category name="eCare Topup" proactiveTimerInSec="30000">
			<pageid>B_MYB_EN_Mobility_Prepaid_OnetimeTopup</pageid>
			<pageid>B_MYB_FR_Mobility_Prepaid_OnetimeTopup</pageid>
			<pageid>B_MYB_EN_Mobility_Prepaid_PreauthorizedTopups</pageid>
			<pageid>B_MYB_FR_Mobility_Prepaid_PreauthorizedTopups</pageid>
		</category>
		<!--<category name="Mock Automatic" proactiveTimerInSec="30000">
		</category>-->
		<!--<category name="Mock Aggressive" proactiveTimerInSec="30000">
		</category>-->
		<!--<category name="Mock Subtle" proactiveTimerInSec="30000">
		</category>-->
	</categories>
	<subtleNotificationLifespan>1200000</subtleNotificationLifespan>
	<omniture>
		<tracking>
			<include>
				<pageid>B_MYB_EN_Login</pageid>
				<pageid>B_MYB_FR_Login</pageid>
				<pageid>B_MYB_EN_Registration_Authentication_EmailChange</pageid>
				<pageid>B_MYB_FR_Registration_Authentication_EmailChange</pageid>
				<pageid>B_MYB_EN_tv_guide</pageid>
				<pageid>B_MYB_FR_tv_guide</pageid>
				<pageid>B_MYB_EN_tv_ppv</pageid>
				<pageid>B_MYB_FR_tv_ppv</pageid>
			</include>
		</tracking>
	</omniture>
</echat>