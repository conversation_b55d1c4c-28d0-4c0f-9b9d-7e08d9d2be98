<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.web>
  </system.web>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="true" />
    <rewrite>
      <rules>
        <clear />
        <rule name="BlockXmlDisplay" patternSyntax="Wildcard" stopProcessing="true">
          <match url="*" />
          <conditions>
            <add input="{URL}" pattern="*.xml" />
          </conditions>
          <action type="Redirect" redirectType="Permanent" url="/Error/Page401" />
        </rule>
		<rule name="BlockUserList" patternSyntax="Wildcard" stopProcessing="true">
          <match url="*" />
          <conditions>
            <add input="{URL}" pattern="*/UserList.txt" />
          </conditions>
          <action type="Redirect" redirectType="Permanent" url="/Error/Page401" />
        </rule>
		<rule name="BlockPasswordDictionary" patternSyntax="Wildcard" stopProcessing="true">
          <match url="*" />
          <conditions>
            <add input="{URL}" pattern="*/PasswordDictionary.txt" />
          </conditions>
          <action type="Redirect" redirectType="Permanent" url="/Error/Page401" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>