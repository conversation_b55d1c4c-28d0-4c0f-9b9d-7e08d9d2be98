<?xml version="1.0" encoding="utf-8"?>

<WebContent xmlns="http://www.brandType.ca/portal/">
	<RecommendationPageConfigurations>
		<PageConfiguration>
			<Page>MyServices</Page>
			<ApplicableChannels>
				<Channel>SelfServe</Channel>
				<Channel>BellCustomerApp</Channel>
				<Channel>PCCustomerApp</Channel>
			</ApplicableChannels>
			<ApplyZoneMaxToAllBans>true</ApplyZoneMaxToAllBans>
			<Zones>
				<Zone>
					<Name>MyServicesHeader</Name>
					<ViewLocation>MyServicesHeaderZone.cshtml</ViewLocation>
					<MaxItems>3</MaxItems>
					<Categories>
						<Category>AddALine</Category>
						<Category>CrossSell</Category>
						<Category>DataAddOn</Category>
						<Category>HardwareUpgrade</Category>
						<Category>OtherAddOn</Category>
						<Category>PlanChange</Category>
						<Category>TravelAddOn</Category>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>true</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
			</Zones>
		</PageConfiguration>
		<PageConfiguration>
			<Page>MobilityOverview</Page>
			<ApplicableChannels>
				<Channel>SelfServe</Channel>
				<Channel>BellCustomerApp</Channel>
				<Channel>PCCustomerApp</Channel>
			</ApplicableChannels>
			<ApplyZoneMaxToAllBans>true</ApplyZoneMaxToAllBans>
			<Zones>
				<Zone>
					<Name>PlanChange</Name>
					<ViewLocation>MobilityOverviewPlanChangeZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>PlanChange</Category>
					</Categories>
					<RequiresLandingPageTarget>true</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>OtherAddOn</Name>
					<ViewLocation>MobilityOverviewOtherAddOnZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>OtherAddOn</Category>
					</Categories>
					<RequiresLandingPageTarget>true</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>DataAddOn</Name>
					<ViewLocation>MobilityOverviewDataAddonZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>DataAddOn</Category>
					</Categories>
					<RequiresLandingPageTarget>true</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>TravelAddOn</Name>
					<ViewLocation>MobilityOverviewTravelAddOnZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>TravelAddOn</Category>
					</Categories>
					<RequiresLandingPageTarget>true</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>AddALine</Name>
					<ViewLocation>MobilityOverviewAddALineZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>AddALine</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>CrossSell</Name>
					<ViewLocation>MobilityOverviewCrossSellZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>CrossSell</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>Hug</Name>
					<ViewLocation>MobilityOverviewHugZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>HardwareUpgrade</Category>
					</Categories>
					<RequiresLandingPageTarget>true</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>Other</Name>
					<ViewLocation>MobilityOverviewOtherZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>true</ExcludeHugConfirmationTarget>
				</Zone>
			</Zones>
		</PageConfiguration>
		<PageConfiguration>
			<Page>Promotion</Page>
			<ApplicableChannels>
				<Channel>SelfServe</Channel>
				<Channel>BellCustomerApp</Channel>
				<Channel>PCCustomerApp</Channel>
			</ApplicableChannels>
			<ApplyZoneMaxToAllBans>false</ApplyZoneMaxToAllBans>
			<Zones>
				<Zone>
					<Name>Hug</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>10</MaxItems>
					<Categories>
						<Category>HardwareUpgrade</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>PlanChange</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>10</MaxItems>
					<Categories>
						<Category>PlanChange</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>AddOns</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>10</MaxItems>
					<Categories>
						<Category>OtherAddOn</Category>
						<Category>DataAddOn</Category>
						<Category>TravelAddOn</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>AddALineAndCrossSell</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>10</MaxItems>
					<Categories>
						<Category>AddALine</Category>
						<Category>CrossSell</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>Other</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>10</MaxItems>
					<Categories>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>true</ExcludeHugConfirmationTarget>
				</Zone>
			</Zones>
		</PageConfiguration>
		<PageConfiguration>
			<Page>ChangeAddOn</Page>
			<ApplicableChannels>
				<Channel>SelfServe</Channel>
				<Channel>BellCustomerApp</Channel>
				<Channel>PCCustomerApp</Channel>
			</ApplicableChannels>
			<ApplyZoneMaxToAllBans>true</ApplyZoneMaxToAllBans>
			<Zones>
				<Zone>
					<Name>ChangeAddOnHeader</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>999</MaxItems>
					<Categories>
						<Category>DataAddOn</Category>
						<Category>OtherAddOn</Category>
						<Category>TravelAddOn</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
			</Zones>
		</PageConfiguration>
		<PageConfiguration>
			<Page>ChangeRatePlan</Page>
			<ApplicableChannels>
				<Channel>SelfServe</Channel>
				<Channel>BellCustomerApp</Channel>
				<Channel>PCCustomerApp</Channel>
			</ApplicableChannels>
			<ApplyZoneMaxToAllBans>true</ApplyZoneMaxToAllBans>
			<Zones>
				<Zone>
					<Name>ChangeRatePlanHeader</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>999</MaxItems>
					<Categories>
						<Category>PlanChange</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
			</Zones>
		</PageConfiguration>
		<PageConfiguration>
			<Page>HardwareUpgrade</Page>
			<ApplicableChannels>
				<Channel>SelfServe</Channel>
				<Channel>BellCustomerApp</Channel>
				<Channel>PCCustomerApp</Channel>
			</ApplicableChannels>
			<ApplyZoneMaxToAllBans>true</ApplyZoneMaxToAllBans>
			<Zones>
				<Zone>
					<Name>HardwareUpgradeHeader</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>999</MaxItems>
					<Categories>
						<Category>HardwareUpgrade</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
			</Zones>
		</PageConfiguration>
		<PageConfiguration>
			<Page>HardwareUpgradeConfirmation</Page>
			<ApplicableChannels>
				<Channel>SelfServe</Channel>
				<Channel>BellCustomerApp</Channel>
				<Channel>PCCustomerApp</Channel>
			</ApplicableChannels>
			<ApplyZoneMaxToAllBans>true</ApplyZoneMaxToAllBans>
			<Zones>
				<Zone>
					<Name>HardwareUpgradeConfirmationHeader</Name>
					<ViewLocation></ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>AddALine</Category>
						<Category>CrossSell</Category>
						<Category>DataAddOn</Category>
						<Category>HardwareUpgrade</Category>
						<Category>OtherAddOn</Category>
						<Category>PlanChange</Category>
						<Category>TravelAddOn</Category>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>true</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
			</Zones>
		</PageConfiguration>
		<PageConfiguration>
			<Page>Preview</Page>
			<ApplicableChannels>
				<Channel>SelfServe</Channel>
				<Channel>BellCustomerApp</Channel>
				<Channel>PCCustomerApp</Channel>
			</ApplicableChannels>
			<ApplyZoneMaxToAllBans>true</ApplyZoneMaxToAllBans>
			<Zones>
				<Zone>
					<Name>MyServicesHeader</Name>
					<ViewLocation>MyServicesHeaderZone.cshtml</ViewLocation>
					<MaxItems>4</MaxItems>
					<Categories>
						<Category>AddALine</Category>
						<Category>CrossSell</Category>
						<Category>DataAddOn</Category>
						<Category>HardwareUpgrade</Category>
						<Category>OtherAddOn</Category>
						<Category>PlanChange</Category>
						<Category>TravelAddOn</Category>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>true</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>MobilityOverviewPlanChange</Name>
					<ViewLocation>MobilityOverviewPlanChangeZone.cshtml</ViewLocation>
					<MaxItems>1</MaxItems>
					<Categories>
						<Category>AddALine</Category>
						<Category>CrossSell</Category>
						<Category>DataAddOn</Category>
						<Category>HardwareUpgrade</Category>
						<Category>OtherAddOn</Category>
						<Category>PlanChange</Category>
						<Category>TravelAddOn</Category>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>true</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>OffersCarousel</Name>
					<ViewLocation>OffersCarouselZone.cshtml</ViewLocation>
					<MaxItems>3</MaxItems>
					<Categories>
						<Category>AddALine</Category>
						<Category>CrossSell</Category>
						<Category>DataAddOn</Category>
						<Category>HardwareUpgrade</Category>
						<Category>OtherAddOn</Category>
						<Category>PlanChange</Category>
						<Category>TravelAddOn</Category>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>HugConfirmationOffers</Name>
					<ViewLocation>HugConfirmationZone.cshtml</ViewLocation>
					<MaxItems>3</MaxItems>
					<Categories>
						<Category>AddALine</Category>
						<Category>CrossSell</Category>
						<Category>DataAddOn</Category>
						<Category>HardwareUpgrade</Category>
						<Category>OtherAddOn</Category>
						<Category>PlanChange</Category>
						<Category>TravelAddOn</Category>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>true</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
				<Zone>
					<Name>Promotions</Name>
					<ViewLocation>PromotionZone.cshtml</ViewLocation>
					<MaxItems>100</MaxItems>
					<Categories>
						<Category>AddALine</Category>
						<Category>CrossSell</Category>
						<Category>DataAddOn</Category>
						<Category>HardwareUpgrade</Category>
						<Category>OtherAddOn</Category>
						<Category>PlanChange</Category>
						<Category>TravelAddOn</Category>
						<Category>Unknown</Category>
					</Categories>
					<RequiresLandingPageTarget>false</RequiresLandingPageTarget>
					<RequiresHugConfirmationTarget>false</RequiresHugConfirmationTarget>
					<ExcludeHugConfirmationTarget>false</ExcludeHugConfirmationTarget>
				</Zone>
			</Zones>
		</PageConfiguration>
	</RecommendationPageConfigurations>
	<!--Reference Section (Read Only)-->
	<!--List of valid offer category values.-->
	<OfferZones>
		<Zone>AddALine</Zone>
		<Zone>CrossSell</Zone>
		<Zone>HardwareUpgrade</Zone>
		<Zone>PlanChange</Zone>
		<Zone>TravelAddOn</Zone>
		<Zone>DataAddOn</Zone>
		<Zone>OtherAddOn</Zone>
		<!--This is a targeting zone if this is used all offer categories will be hidden on MyServices and MobilityOverview-->
		<Zone>LandingPage</Zone>
		<!--This is a targeting zone if this is used all offer categories will be hidden on Hardware Confirmation-->
		<Zone>HardwareUpgradeConfirmation</Zone>
	</OfferZones>
	<!--List of valid channel category values.-->
	<Channels>
		<Channel>SelfServe</Channel>
		<Channel>BellCustomerApp</Channel>
    <Channel>VirginCustomerApp</Channel>
		<Channel>LuckyCustomerApp</Channel>
		<Channel>PCCustomerApp</Channel>
		<Channel>OneView</Channel>
		<Channel>WacView</Channel>
		<Channel>SingleView</Channel>
		<Channel>AgentTool</Channel>
		<Channel>BBM</Channel>
		<Channel>CSM</Channel>
	</Channels>
</WebContent>