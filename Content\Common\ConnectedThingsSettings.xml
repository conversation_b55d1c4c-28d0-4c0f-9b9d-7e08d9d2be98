<?xml version="1.0" encoding="utf-8"?>
<Configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <settings>
    <add key="ConnectedThings.ONLINE.SecretKey" value="GfHjW83eUqiFHojU" />
    <add key="ConnectedThings.ONLINE.BackURLEn" value="{0}/Mobility/ActivateConnectedThings" />
    <add key="ConnectedThings.ONLINE.BackURLFr" value="{0}/Mobilite/ActiverObjetsConnectes" />
    <add key="ConnectedThings.ONLINE.ConfirmOrderRetries" value="1" />
    <add key="ConnectedThings.EGW.SecretKey" value="TgoheuRByeLcUfDg" />
    <add key="ConnectedThings.EGW.ConfirmOrderRetries" value="1" />
    <add key="ConnectedThings.ONLINE.BrowsingCategory.Promo" value="75631881" />
    <add key="ConnectedThings.ONLINE.BrowsingCategory.NoPromo" value="75631891" />
    <add key="ConnectedThings.EGW.BrowsingCategory.Promo" value="75633701" />
    <add key="ConnectedThings.EGW.BrowsingCategory.NoPromo" value="75633711" />    
    <add key="ConnectedThings.ThresholdAmount" value="2" />
    <add key="ConnectedThings.SleepTimeBeforeBupLink" value="600" />
    <add key="ConnectedThings.NumberOfPhoneNumbersToReserve" value="10" />
    <add key="ConnectedThings.TimeToWaitBeforePhoneRelease" value="10" />
    <add key="ConnectedThings.ConfirmationEmailDefaultBannerImageURL" value="/Styles/wireless/all_languages/all_regions/catalog_images/EGW_Banners/Laptop_Banner.png" />
    <add key="ConnectedThings.MaxNumberOfCreditCardValidationAttempts" value="5" />
    <add key="ConnectedThings.AllowanceThresholdAmount" value="99999" />
    <add key="ConnectedThings.TopupAmount" value="5" />
    <add key="ConnectedThings.Allowance_SOP_Y" value="920" />
    <add key="ConnectedThings.AutoTopUp_SOP_Y" value="903" />
    <add key="ConnectedThings.AutoTopUp_SOP_N" value="921" />
    <add key="ConnectedThings.AutoAllowance_SOP_N" value="922" />
    <add key="ConnectedThings.AutoAllowance_SOP_Y" value="923" />
  </settings>
</Configuration>
