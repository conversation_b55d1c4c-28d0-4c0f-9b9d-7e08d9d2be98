<?xml version="1.0" encoding="UTF-8"?>
<ServiceProviderSpecList>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>2782</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>TELUS</ProviderNameEn>
<ProviderNameFr>TELUS</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>4878</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Eastlink</ProviderNameEn>
<ProviderNameFr>Eastlink</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NB</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NS</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>PE</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NL</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>081E</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Distributel</ProviderNameEn>
<ProviderNameFr>Distributel</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>493F</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Yak Communications</ProviderNameEn>
<ProviderNameFr>Yak Communications</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8306</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Videotron</ProviderNameEn>
<ProviderNameFr>Videotron</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>138D</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Wightman Communications</ProviderNameEn>
<ProviderNameFr>Wightman Communications</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{8}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>4727</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>ISP Telecom</ProviderNameEn>
<ProviderNameFr>ISP Telecom</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>995D</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Nexicom Telecommunication</ProviderNameEn>
<ProviderNameFr>Nexicom Telecommunication</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8304</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Allstream</ProviderNameEn>
<ProviderNameFr>Allstream</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>201A</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Globility</ProviderNameEn>
<ProviderNameFr>Globility</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8257</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Telephone Drummond</ProviderNameEn>
<ProviderNameFr>Telephone Drummond</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8083</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Telus Solutions Que Inc</ProviderNameEn>
<ProviderNameFr>Telus Solutions Que Inc</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>277E</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Tuckersmith Co-op</ProviderNameEn>
<ProviderNameFr>Tuckersmith Co-op</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8821</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Rogers Wireless</ProviderNameEn>
<ProviderNameFr>Rogers Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>582F</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Chatr Wireless</ProviderNameEn>
<ProviderNameFr>Chatr Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>646F</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Public Mobile Wireless</ProviderNameEn>
<ProviderNameFr>Public Mobile Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>328F</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Vidéotron Wireless</ProviderNameEn>
<ProviderNameFr>Vidéotron Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>4297</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Futureway Comm</ProviderNameEn>
<ProviderNameFr>Futureway Comm</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>464D</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Fibernetics</ProviderNameEn>
<ProviderNameFr>Fibernetics</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>154E</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Iristel Inc</ProviderNameEn>
<ProviderNameFr>Iristel Inc</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>6574</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Bell Mobility Wireless</ProviderNameEn>
<ProviderNameFr>Bell Mobilité Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8303</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Telus Mobility Wireless</ProviderNameEn>
<ProviderNameFr>Telus Mobility Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>5643</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Fido Wireless</ProviderNameEn>
<ProviderNameFr>Fido Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>3147</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>ExaTEL</ProviderNameEn>
<ProviderNameFr>ExaTEL</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>190E</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Telnet Communications</ProviderNameEn>
<ProviderNameFr>Telnet Communications</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8226</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Execulink Telecom Inc</ProviderNameEn>
<ProviderNameFr>Execulink Telecom Inc</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8506</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Group Telecom</ProviderNameEn>
<ProviderNameFr>Group Telecom</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>940D</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>North Renfrew Tel</ProviderNameEn>
<ProviderNameFr>North Renfrew Tel</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>660E</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Ontera</ProviderNameEn>
<ProviderNameFr>Ontera</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>920D</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Westport Telephone</ProviderNameEn>
<ProviderNameFr>Westport Telephone</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>400E</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Mornington Communications</ProviderNameEn>
<ProviderNameFr>Mornington Communications</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>405E</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Huron Tel</ProviderNameEn>
<ProviderNameFr>Huron Tel</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>817D</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>IVIC Telecom</ProviderNameEn>
<ProviderNameFr>IVIC Telecom</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>383F</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Freedom Wireless</ProviderNameEn>
<ProviderNameFr>Freedom Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8303</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Koodoo Wireless</ProviderNameEn>
<ProviderNameFr>Koodoo Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>6574</SPID>
<AlternateSPID>329A</AlternateSPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Virgin Wireless</ProviderNameEn>
<ProviderNameFr>Virgin Wireless</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Wireless</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>575G</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Beanfield</ProviderNameEn>
<ProviderNameFr>Beanfield</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>2776</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Managed Networks</ProviderNameEn>
<ProviderNameFr>Managed Networks</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>548H</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Teksavvy</ProviderNameEn>
<ProviderNameFr>Teksavvy</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{9}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{9}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8084</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Telus Alberta</ProviderNameEn>
<ProviderNameFr>Telus Alberta</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>AB</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Telus BC</ProviderNameEn>
<ProviderNameFr>Telus BC</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>BC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2015-09-13'>
<SPID>8050</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
<Clec>false</Clec>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2015-09-13'>
<ProviderNameEn>Bell Canada Quebec</ProviderNameEn>
<ProviderNameFr>Bell Canada Quebec</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>false</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2015-09-13'>
<SPID>8051</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
<Clec>false</Clec>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2015-09-13'>
<ProviderNameEn>Bell Canada Ontario</ProviderNameEn>
<ProviderNameFr>Bell Canada Ontario</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>false</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>743B</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Rogers</ProviderNameEn>
<ProviderNameFr>Rogers</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-30'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>NB</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{9}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NS</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{9}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>PE</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{9}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NL</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{9}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{9}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{12}$</characterType>
</ServiceProviderAccNumberValidations>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{9}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>497E</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Shaw</ProviderNameEn>
<ProviderNameFr>Shaw</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>AB</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>BC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NS</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NT</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NU</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NB</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>MB</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>PE</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>SK</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>YT</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>NL</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{11}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>4591</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Cogeco</ProviderNameEn>
<ProviderNameFr>Cogeco</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2018-05-06'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2019-05-24'>
<SPID>160G</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2019-05-24'>
<ProviderNameEn>Comwave</ProviderNameEn>
<ProviderNameFr>Comwave</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2019-05-24'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[a-zA-Z0-9]{8,16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>818D</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Cooptel</ProviderNameEn>
<ProviderNameFr>Cooptel</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>247F</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Xittel Telecommunications</ProviderNameEn>
<ProviderNameFr>Xittel Telecommunications</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>8254</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Maskatel Inc</ProviderNameEn>
<ProviderNameFr>Maskatel Inc</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2023-10-15'>
<SPID>654J</SPID>
<FieldWorkInd>true</FieldWorkInd>
<CableTechnology>true</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2023-10-15'>
<ProviderNameEn>Cablevision</ProviderNameEn>
<ProviderNameFr>Cablevision</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{7}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{7}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2023-10-15'>
<SPID>984C</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2023-10-15'>
<ProviderNameEn>Bell Canada as CLEC</ProviderNameEn>
<ProviderNameFr>Bell Canada comme ESLC</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>false</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>2243</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Telus Quebec</ProviderNameEn>
<ProviderNameFr>Telus Quebec</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2023-10-15'>
<SPID>8210</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2023-10-15'>
<ProviderNameEn>DMTS</ProviderNameEn>
<ProviderNameFr>DMTS</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[0-9]{1,6}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2023-10-15'>
<SPID>8218</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2023-10-15'>
<ProviderNameEn>KMTS</ProviderNameEn>
<ProviderNameFr>KMTS</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[0-9]{1,6}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2023-10-15'>
<SPID>988H</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2023-10-15'>
<ProviderNameEn>TBayTel</ProviderNameEn>
<ProviderNameFr>TBayTel</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2023-10-15'>
<SPID>8239</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2023-10-15'>
<ProviderNameEn>Télébec</ProviderNameEn>
<ProviderNameFr>Télébec</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Range Of Digits</accFormat>
<characterType>^[0-9]{6,8}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2023-10-15'>
<SPID>843D</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2023-10-15'>
<ProviderNameEn>Sogetel</ProviderNameEn>
<ProviderNameFr>Sogetel</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Internet'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Television'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[a-zA-Z0-9]{16}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2023-10-15'>
<SPID>940G</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2023-10-15'>
<ProviderNameEn>Ssi Micro</ProviderNameEn>
<ProviderNameFr>Ssi Micro</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2023-10-15'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>QC</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
<ServiceProviderSpec>
<ServiceProviderGeneralAttributeList>
<ServiceProviderGeneralAttributeSpec  effectiveDate='2017-04-23'>
<SPID>038E</SPID>
<FieldWorkInd>false</FieldWorkInd>
<CableTechnology>false</CableTechnology>
</ServiceProviderGeneralAttributeSpec>
</ServiceProviderGeneralAttributeList>
<ServiceProviderNameList>
<ServiceProviderName  effectiveDate='2017-04-23'>
<ProviderNameEn>Bruce Telecom</ProviderNameEn>
<ProviderNameFr>Bruce Telecom</ProviderNameFr>
</ServiceProviderName>
</ServiceProviderNameList>
<ServiceProviderConfigConditionsList>
<ServiceProviderConfigConditions  effectiveDate='2017-04-23'>
<DisplayinUI>true</DisplayinUI>
<ServiceProviderType>Local</ServiceProviderType>
<RegionalizedConfigSpecList>
<RegionalizedConfigSpec>
<Province>ON</Province>
<LineOfBusinessList>
<LineOfBusiness  lobID='Wireline'>
<ServiceProviderAccNumberValidationsList>
<ServiceProviderAccNumberValidations>
<accFormat>Fixed</accFormat>
<characterType>^[0-9]{10}$</characterType>
</ServiceProviderAccNumberValidations>
</ServiceProviderAccNumberValidationsList>
</LineOfBusiness>
</LineOfBusinessList>
</RegionalizedConfigSpec>
</RegionalizedConfigSpecList>
</ServiceProviderConfigConditions>
</ServiceProviderConfigConditionsList>
</ServiceProviderSpec>
</ServiceProviderSpecList>
