<?xml version="1.0" encoding="utf-8"?>
<Configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<settings>

		<!--Suspend & Restore: Value ON / OFF / -->
		<add key="IsTempSuspendAndRestoreEnabled" value="ON"/>
		<add key="IsTempSuspendAndRestoreEnabledForPrepaid" value="ON"/>
		<add key="PrepaidUsage" value="ON"/>
		<!--PersonalizeToggle: Value ON / OFF / PILOT -->
		<add key="PersonalizeToggle" value="ON" />

		<!--Bell Self Repair Toggle: Value ON / OFF-->
		<add key="EnableSelfRepair" value="OFF" />
		<add key="SelfRepairPilotToggle" value="OFF" />
		<add key="EnableSelfRepairAppointment" value="ON" />

		<add key="EnableVirtualRepair" value="ON" />
		<add key="ShowScheduleCallback" value="OFF" />
		<add key="ProgressiveProfileToggle" value="ON" />

		<add key="PersonalizationNSIToggle" value="ON" />


		<!--WHI PH4 Display: Value ON / OFF-->
		<add key="WHIOnOffToggle" value="ON" />
		<add key="WHICR10OnOffToggle" value="ON" />
		<add key="WHICR0OnOffToggle" value="ON" />
		<add key="WHICR52OnOffToggle" value="ON" />
		<add key="WHICR52OnOffToggleForApp" value="ON" />

		<!--POD Display: Value ON / OFF-->
		<add key="PODOnOffToggle" value="ON" />
		<add key="PODServiceDisableforWHI" value="OFF" />
		<add key="AllowPodsToMBM" value="ON" />

		<!--HH4K PH2 Display: Value ON / OFF-->
		<add key="BatteryPowerOutageLightBoxONOFFToggle" value="ON" />

		<add key="PASSWORD_ENHANCEMENT_FORCED" value="ON" />
		<add key="PASSWORD_ENHANCEMENT_FORCED_APP" value="OFF" />

		<!--OTTO Display: Value ON / OFF-->
		<add key="OTTODisplayToggle" value="ON" />
		<!--Battery Warranty Info Display: Value ON / OFF-->
		<add key="BatteryWarrantyDisplayToggle" value="ON" />
		<!--Show delinquency notifications for Mobility accounts - Values: ON / OFF -->
		<add key="MobilityDelinqencyToggle" value="ON" />
		<!--OTTO - Upsell: Value ON / OFF-->
		<add key="OTTPromoDisplayToggle" value="ON" />
		<!-- NM1 Display : Value ON / OFF -->
		<add key="NM1DisplayToggle" value="ON" />

		<!--Show Advance Pay by Date - Values: ON / OFF -->
		<add key="IsAPBDEnabled" value="ON" />

		<!--Show Churchill Ph2 - Values: ON / OFF -->
		<add key="IsChurchillPh2Enabled" value="ON" />

		<!--Added for INC000006172604, TFS# - 259054 -->
		<add key="GetMobilitySubscriberByMdnCancelled" value="ON" />

		<!--Show CDNR Ph2 - Values: ON / OFF -->
		<add key="IsCDNRPh2Enabled" value="ON" />

		<!--Email endpoints for customer surveys - Values: ON / OFF -->
		<add key="PostOfflineProcessingInfoToDigiTekService" value="ON" />

		<!--Email endpoints for customer surveys - Values: ON / OFF -->
		<add key="PostOfflineProcessingInfoToKana" value="ON" />

		<!--Added for INC000006283031, TFS# 4315 -->
		<add key="HUBRegistrationLinking" value="ON" />

		<add key="RecoveryForgotBothToggle" value="OFF" />

		<add key="RecoveryForgotUsernameToggle" value="ON" />
		<add key="RecoveryForgotPasswordToggle" value="ON" />

		<add key="CaptchaLoginToggle" value="ON" />
		<add key="CaptchaV3LoginToggle" value="ON" />
		<add key="CaptchaRegistrationToggle" value="ON" />
		<add key="CaptchaForgotBothToggle" value="OFF" />
		<add key="CaptchaForgotUsernameToggle" value="ON" />
		<add key="CaptchaForgotPasswordToggle" value="ON" />

		<add key="IsNoCacheEnableForHome" value="ON" />

		<add key="IsNoCacheEnable" value="ON" />
		<add key="SanitizeDataWidgetInputRequest" value="ON" />
		<add key="IsNoCacheEnableForReg" value="ON" />
		<add key="IsNoCacheEnableForLogin" value="ON" />

		<add key="Enable_MOIProfile" value="ON" />
		<!--WSS WSS_DisableAll will turn off all WSS -->
		<add key="WSS_DisableAll" value="OFF" />
		<add key="IsNoCacheEnableForUXP" value="OFF" />

		<add key="WSS_LandingPages" value="ON" />
		<add key="WSS_TransactionalFlows" value="ON" />
		<add key="WSS_UnlockSimFlow" value="ON" />
		<add key="WSS_UnlockDeviceFlow" value="ON" />
		<add key="WSS_DALFlow" value="ON" />
		<add key="WSS_PendingTransactionFlow" value="ON" />
		<add key="WSS_ManageDataFlow" value="ON" />
		<add key="WSS_ManageUsageAndTravelFlow" value="ON" />
		<add key="WSSDefaultCMOEnable" value="OFF" />
		<add key="WSSAllowOverrides" value="ON" />
		<add key="Show_NoData_Option_For_AddRemove_Features" value="ON" />
		<add key="Show_NoData_Option_For_Manage_Usage" value="ON" />
		<add key="Show_NoData_Option_For_Manage_Data" value="ON" />
		<add key="Show_NoChange_Option_For_AddRemove_Features" value="ON" />
		<add key="Show_NoChange_Option_For_Manage_Usage" value="ON" />
		<add key="Show_NoChange_Option_For_Manage_Data" value="ON" />
		<add key="EHSImagePath" value="https://mybell.bell.ca" />
		<add key="Categories" value="" />

		<!--Added for Manage Travel BussineAccount Banner Toggle -->
		<add key="Enable_CDNR_BANNER" value="ON" />
		<add key="ConnectedCarCountryName" value="USA" />
		<add key="FAQ_Search_Enable_ManageTravel" value="ON" />
		<add key="RomeBetterBanner_Enable_ManageTravel" value="OFF" />
		<add key="NM1_SOC_Enable_ManageTravel" value="ON" />
		<add key="Syniverse_SOC_Enable_ManageTravel" value="ON" />

		<!--Added for Error Page-->
		<add key="WSS_ErrorPages" value="ON" />

		<!--Added for Activate Device Submission -->
		<add key="WSS_Activate_Device_Submit" value="ON" />
		<add key="MobilityDelinqencyToggle" value="ON" />

		<!-- Added for CR51 COM Error Fix -->
		<add key="IsSetChannelForSalesRep" value = "ON" />

		<add key="WSS_TransferFlow_Verify_Status" value="ON" />
		<add key="WSS_TransferFlow_Submit_Status" value="ON" />

		<!--Added for Proration calculation-->
		<add key="Total_Bill_Dates" value = "30" />

		<!--WSS DEVICE INSTALLMENT TOGGLE-->
		<add key="WSS_DeviceInstallment" value = "ON" />

		<!--WSS HUG SUBMIT TOGGLE -->
		<add key="WSS_HUG_Submit" value = "ON" />

		<add key="PASSWORD_ENHANCEMENT" value = "ON" />

		<!--ENABLE STAGING BAZARVOICE -->

		<add key="ENABLE_STAG_BAZARVOICE" value = "OFF" />

		<!--CRP EffectiveDate settings-->
		<add key="CRP_EFFECTIVE_DATE_SETTING_TOGGLE" value = "ON" />
		<add key="CRP_EFFECTIVE_TODAY" value = "ON" />
		<add key="CRP_EFFECTIVE_CURRENT_BILLING_PERIOD" value = "OFF" />
		<add key="CRP_EFFECTIVE_NEXT_BILLING_PERIOD" value = "ON" />

		<!--CRP No Data Display settings-->
		<add key="CRP_Show_No_Data" value = "ON" />
		<add key="CRP_StandAloneFlow" value = "ON" />
		<add key="CRP_HugFlow" value = "ON" />
		<add key="CRP_TransferServiceFlow" value = "ON" />

		<!--If set OFF then data selection is mandate-->
		<add key="LEAVING_SHARE_GROUP" value="ON" />

		<!--Show multiline offer lightbox-->
		<add key="SHOW_MULTILINE_OFFER" value="ON" />

		<add key="DATA_SELECTION" value="OFF" />

		<!--HUG Promo / Pre -Order Flags-->
		<add key="HUG_PROMO_PREORDER_FLAG" value="ON" />

		<!--Client eCare Roaming Enhancement Ph2-->
		<add key="ECARE_ROAMING_ENHANCEMENT_PH2" value="ON" />

		<!--Smart Pay Monthly Rebates toggle Bamboo smartPay-->
		<add key="SMART_PAY_MONTHLY_REBATES" value="ON" />

		<!--Client eCare Roaming Enhancement Ph2-->
		<add key="FETCH_NEW_TRAVEL_MATCH_SERVICE" value="ON" />

		<!--ECare Bamboo economic inducement Phase4-->
		<add key="BAMBOO_ECONOMIC_INDUCEMENT" value="ON" />

		<!--ECare Bamboo Direct Fullfillment Phase5-->
		<add key="BAMBOO_DIRECT_FULLFILLMENT" value="ON" />

		<add key="BAMBOO_HUG_REDESIGN" value="ON" />

		<add key="EnableWeakPasswordPolicy" value="OFF" />

		<add key="EnableInternetUsagesAuthentication" value="ON" />
		<add key="EnableSecureAuthCookieForMyBell" value="ON" />
		<add key="ServiceAccountCSRF" value="OFF" />
		<add key="Validate_RequestOrigin" value="OFF" />
		<add key="ServiceAccountCSRF" value="ON" />
		<add key="Validate.cc.gps" value="ON" />
		<add key="IsShowPurchase" value="OFF" />

		<add key="CaptchaV3LoginFlowToggle" value="ON" />
		<add key="CaptchaV3ForgotBothToggle" value="ON" />
		<add key="CaptchaV3ForgotPassWordToggle" value="ON" />
		<add key="CaptchaV3ForgotUserNameToggle" value="ON" />
		<add key="CaptchaV3RegistrationToggle" value="ON" />
		<add key="CaptchaV3BundleLoginToggle" value="ON" />
		<add key="CaptchaV3AddalineLoginToggle" value="ON" />

		<!--This is for Enterprise Recaptcha-->
		<add key="CaptchaEnterpriseAddaLineLoginToggle" value="ON" />
		<add key="CaptchaEnterpriseBundleLoginToggle" value="ON" />
		<add key="CaptchaEnterpriseAlwaysOnToggle" value="OFF" />

		<!-- This is for PT team.  Keep it to OFF-->
		<add key="CaptchaV3AlwaysOnToggle" value="OFF" />
		<add key="CaptchaV3AlwaysOn_Recovery_Toggle" value="OFF" />

		<add key="CaptchaEnterpriseForgotUserNameToggle" value="ON" />
		<add key="CaptchaEnterpriseForgotPasswordToggle" value="ON" />
		<add key="CaptchaEnterpriseForgotBothToggle" value="OFF" />
		<add key="CaptchaEnterpriseLoginFlowToggle" value="ON" />
		<add key="CaptchaEnterpriseLoginToggle" value="ON" />
		<add key="CaptchaEnterpriseRegistrationToggle" value="ON" />
		<add key="CaptchaEnterpriseAlwaysOn_Recovery_Toggle" value="OFF" />

		<add key="LOGIN_FCC_CLIENT_TOGGLE" value="ON" />
		<add key="SECURITY_PROTOCOL" value ="ON" />
		<add key="SKIP_SSL_Certificate" value ="ON" />

		<add key ="IsEppEnabled" value="ON" />
		<add key="IsSatTvEnabled" value="ON" />
		<add key="IsPrepaidUsageAPINoCache" value="ON" />
		<add key="IsLocalizationAPINoCache" value="ON" />
		<add key="IsPostpaidUsageAPINoCache" value="ON" />
		<add key="IsBillingAccountsAPINoCache" value="ON" />
		<add key="IsProfileMobilityAPINoCache" value="ON" />
		<add key="IsPersonalizedContentApiNoCache" value="ON" />
		<add key="IsXContentTypeOptions" value="ON" />
		<add key="Validate_RequestOriginWithApplicationType" value="OFF" />
		<add key="IsXSSProtectionEnabled" value="ON" />
		<add key="ConnectedDeviceToggle" value="ON" />
		<add key="CovidInstallationScreeningToggle" value="OFF" />
		<add key="WCOCAddDataToggle" value ="ON" />

		<!-- Switch ON OFF the Edit Email should be OFF on Prod -->
		<add key="canEditEmail" value="OFF" />
		<!-- Change to OFF based on eSIM CR 19 -->
		<add key="ConnectedThings.IsTailoredMarketingEnabled" value="OFF" />
		<!-- ADDING ECARE (MYBELL) Toggle OVER HERE FOR RTUD  -->
		<add key="IsRealTimeUsageDetails" value="ON" />
		<add key="BELL_EnableRealTimeUsage" value="ON" />
		<add key="BELL_MYB_EnableRealTimeUsage" value="ON" />
		<add key="BELL_MBM_EnableRealTimeUsage" value="ON" />
		<add key="BELL_EnableTieredUsage" value="ON" />
		<add key="BELL_MYB_EnableTieredUsage" value="ON" />
		<add key="BELL_MBM_EnableTieredUsage" value="ON" />
		<add key="AllowOverrideOfConsolidatedUsage" value="ON" />

		<add key="RecoveryForgotBothToggleModelValidation" value="ON" />

		<add key="RegistrationLoginAccessDisabled" value="ON" />

		<add key="UserProfileApiNoCache" value="ON" />

		<add key="BELL_Login_Tracker" value="OFF" />
		<add key="BELL_Login_Tracker_Concurrent_Session" value="ON" />
		<add key="BELL_Login_Tracker_Notification_Bar" value="ON" />
		<add key="BELL_Login_Tracker_IpAddress" value="ON" />
		<add key="BELL_Login_Tracker_Location" value="ON" />
		<add key="BELL_Login_Tracker_Browser" value="ON" />
		<add key="BELL_Login_Tracker_DeviceType" value="ON" />
		<add key="BELL_Login_TrackerL_DateTime" value="ON" />

		<add key="Quick_Fixes_Internet_Validation" value="ON" />

		<add key="IsDROEnable" value="OFF" />

		<add key="NDA_RAIN" value="ON" />

		<add key="HUG_TIER_INSTALLMENT" value="ON" />
		<add key="RATEPLAN_HARDWAREPROMOCALL" value="OFF" />

		<add key="IDVLoginFCC" value ="OFF" />

		<add key="Validate_RequestOriginWithApplicationType" value="ON" />
		<add key="Http_Session_On_Off" value="ON" />
		<add key="SmSession_On_Off" value="ON" />
		<add key="Throttling_Link_Account" value="ON" />
		<add key="Validate_Sim_Device" value="ON" />
		<add key="WNP_NOTIFICATION_CHANGE" value="ON" />
		<add key="WNP_PORTOUT_AUTHORIZATION" value="OFF" />

		<add key="Recaptcha_MOCK_Enabled" value="OFF" />
		<add key="UsageEnhancementMockEnabled" value="OFF" />
		<add key="UsageEnhancementRetrieveAvailablePPMockEnabled" value="OFF" />
		<add key="UsageEnhancementGetGroupSharingDetailsMockEnabled" value="OFF" />
		<add key="UsageEnhancementGetAssignedSOCsAndFeaturesMockEnabled" value="OFF" />
		<add key="IsUnlimitedUsageEnhancementEnableForAPI" value="OFF" />

		<add key="ISDRO_ELIGIBLE_FORUPGRADE" value="ON" />
		<add key="Is90DaysAndIs12MonthsHUGSkipEnable" value="OFF" />

		<add key="Quick_Fixes_Hide_Bank_Number" value="OFF" />

		<add key="IsUtilitiesAPINoCache" value="ON" />

		<add key="RTC_Mobility_ChangeRatePlan_OnOffToggle" value="ON" />
		<add key="RTC_Mobility_HUG_OnOffToggle" value="ON" />
		<add key="RTC_Mobility_UnlockDevice_OnOffToggle" value="ON" />
		<add key="RTC_HomePhone_ChangePackage_OnOffToggle" value="ON" />
		<add key="RTC_Internet_ChangePackage_OnOffToggle" value="ON" />
		<add key="RTC_Internet_ChangeFeatures_OnOffToggle" value="ON" />
		<add key="RTC_Internet_AddUnlimitedUsageVAS_OnOffToggle" value="ON" />
		<add key="RTC_TV_OnOffToggle" value="ON" />

		<add key="RTN_Login_OnOffToggle" value="ON" />
		<add key="RTN_Mobility_ChangeRatePlan_OnOffToggle" value="OFF" />
		<add key="RTN_Mobility_HUG_OnOffToggle" value="OFF" />

		<add key="SVHUGCR03_OffMode_OnOffToggle" value="OFF" />
		<add key="SVHUGCR03_ChooseDeviceLightBox_Enable" value="OFF" />

		<add key="RescheduleProvisioningToggle" value="ON" />
		<add key="RescheduleCancelAssuranceToggle" value="ON" />
		<add key="TechETAToggle" value="ON" />
		<add key="MYAEChat" value="ON" />

		<add key="IsPCIBankingTxnEnabled" value="ON" />

		<!--RecommendationToggle: Value ON / OFF-->
		<add key="RecommendationApiIsEnabled" value="ON" />
		<add key="RecommendationApiIsEnabledForMobile" value="ON" />
		<add key="RecommendationContentIsNotCached" value="ON" />
		<add key="RecommendationsShownForNonAccountOwners" value="OFF" />

		<add key="ESIMChangeSIMDeviceEnabled" value="ON" />
		<add key="HUGActivationMockDownloadOrderConfirmOrderEnabled" value="OFF" />
		<add key="HUGActivationMockConfirmOrderAlwwaysFailEnabled" value="OFF" />
		<add key="ESIMChangeSIMDeviceBypassOwnershipVerificationEnabled" value="ON" />
		<add key="ESIMChangeSIMDeviceMockSubmitEnabled" value="OFF" />

		<add key="PCI_Compliance_CC_Enabled" value="ON" />

		<!--SeasonalSuspension: Value ON / OFF-->
		<add key="SeasonalSuspensionToggle" value="ON" />
		<add key="HUG_SPC" value="ON" />
		<add key ="MYA_LINK_TOGGLE" value ="ON" />
		<add key="IsPCIPINTxnEnabled" value="ON" />
		<add key="IsAALToggleEnabled" value="ON" />
		<add key="Send_AJAX_AntiForgery_Header" value="ON" />

		<add key="MVMProgressiveProfileToggle" value="ON" />
		<add key="BAMBOO_PH8B" value="ON" />


		<add key="InvoiceInNM1Toggle" value="ON" />

		<add key="DataManagerToggle" value="ON" />
		<add key="DataManagerScheduleToggle" value ="ON" />

		<add key="UnlimitedShrCR01OnOffToggle" value="ON" />
		<add key="UnlimitedShrCR02OnOffToggle" value="ON" />

		<add key="ShippingTracker_eCare" value="ON" />
		<add key="ShippingTracker_Apps" value="ON" />
		<add key="HUG_SPC" value="ON" />
		<add key="HUG_SPC_eCare" value="ON" />

		<add key="TRADEIN_ONLINE" value="ON" />
		<add key="HUG_SPC_Apps" value="ON" />
		<add key="Bell_ValidateReturnURL_Parameter" value="ON" />
		<add key="MANAGE_RECEIVER_FLOW" value="ON" />
		<add key="MANAGE_RECEIVER_PHASE1_FLOW" value="ON" />
		<add key="MANAGE_RECEIVER_INTERCEPT_FLOW" value="ON" />
		<add key="FibeDTHNM1Enabled" value="ON" />
		<add key="FibeDTH_NM1_Phase7_Enabled" value="ON" />
		<add key="FibeDTH_NM1_BFF_Enable" value="ON" />
		<add key="RepToOMFToggle" value="ON" />

		<add key="Echat_Evolution" value="ON"/>
		<add key="GPSTogglePaymentVelocityAttemptsError" value="ON"/>
		<add key="CommunityForumToggle" value="ON" />
		<add key="IsSLOLogoutEnable" value="ON"/>
		<add key="DtsEncryptionToggle" value="OFF" />

		<add key="SmallBusinessRatePlans_eCare" value="ON"/>
		<add key="SmallBusinessRatePlans_Apps" value="ON"/>

		<!--NBA CRP MasFixes Toggle: Value ON / OFF-->
		<add key="NBA_CRPMasFixEnabled" value="ON" />
		<add key="MIGRATION_RECEIVERS_FLOW" value="ON" />
		<add key="BARRT_MOCK_RESPONSE_ON" value="OFF" />

		<add key="BoBoCrave_eCare" value="ON"/>
		<add key="BoBoCrave_Apps" value="ON"/>
		<add key="BoBoCrave_CR18_eCare" value="ON"/>
		<add key="BoBoCrave_CR18_Apps" value="ON"/>
		<add key="MROAToggle" value="ON" />
		<add key="TVODToggle" value="OFF" />
		<add key="BoBoCrave_Ph1A_eCare" value="ON"/>
		<add key="BoBoCrave_Ph1A_Apps" value="ON"/>
		<add key="BoBoCrave_Ph1B_eCare" value="ON"/>
		<add key="BoBoCrave_Ph1B_Apps" value="ON"/>
		<!--TailoredMarketing Toggle: Value ON / OFF-->
		<add key="MYB_TM_INTERCEPT_FLOW" value="ON"/>
		<add key="HUG_LowStockStatus_eCare" value="OFF" />
		<add key="HUG_LowStockStatus_Apps" value="OFF" />
		<add key="UseFillPubSrvsInfoV2" value="ON" />
		<add key="NBA_RealTimeEventPublisherToggle" value="ON" />

		<!--Outage notifications Toggle: Value ON / OFF-->
		<add key="BellOutageBannerToggle" value="ON"/>
		<add key="MROA_PH2_Toggle" value="ON"/>
		<add key="MROA_SELFINSTALL_BLOCK_Toggle" value="OFF"/>

		<!--MROA Ph2 Part2: Upgrade Receiver to Android Toggle: Value ON / OFF-->
		<add key="isMROAUpgradeReceiverEnabled" value="OFF" />
		<!--MROA Ph2 Part2: Upgrade Receiver to Android Self Install Block Toggle: Value ON / OFF-->
		<add key="MROA_SELFINSTALL_MIGRATION_BLOCK_Toggle" value="OFF" />

		<!--BCRIS Bypass Toggle: Value ON / OFF-->
		<add key="BCRIS_OnOffToggle" value="ON" />

		<!--GeS Hardening Toggle: Value ON / OFF-->
		<add key="GeS_Hardening_Bell" value="ON"/>
		<add key="GeS_Hardening_Bell_Logging" value="OFF"/>

		<add key="ZoneConfigFilePath" value="~/contentshop/RSX/Common/Data/ZoneConfig.xml" />

		<add key="isVUPatorderCR021enable" value="ON" />
		<add key="DRO_HISTORICAL_VIEW" value="ON" />
		<add key="Bell_eCareCASL_Flow" value="ON"/>
		<add key="BuyFlowAutomation_AAL" value="ON"/>
		<add key="SMB_AAL" value="ON"/>
		<add key="FibeDTH_OneView_REPToOMF_Toggle" value="ON"/>
		<add key="FibeDTH_PH5" value="ON"/>
		<add key="WCOE_eCare" value="ON"/>
		<add key="WCOE_Apps" value="ON"/>
		<add key="BILL_96" value="ON"/>
		<add key="Bill96_CR05_Toggle" value="ON"/>
		<add key="DAP_Rating_Toggle" value="ON"/>
		<add key="DAP_Kafka_Toggle" value="ON"/>
		<add key="FibeDTHCR194Enabled" value="ON"/>
		<add key="MYB_LoginRequestHeaders" value="OFF"/>
		<add key="USE_SERVER_MAILID" value = "ON" />
		<add key="OrderingServicesLoggingEnabled" value="ON"/>
		<add key="ServiceBasedRatePlans_eCare" value="ON" />
		<add key="ServiceBasedRatePlans_Apps" value="ON" />
		<add key="ServiceBasedAddons_eCare" value="ON" />
		<add key="ServiceBasedAddons_Apps" value="ON" />
		<add key="IsESIMScalingEnabled" value="ON"/>
		<add key="ESIM_AS_PRIMARY" value="ON"/>
		<add key="ESIM_AS_PRIMARY_CR" value="ON"/>
		<add key="BMRO_eCare" value="ON"/>
		<add key="BMRO_Apps" value="ON"/>
		<!--SummaryOfChanges: Value ON / OFF-->
		<add key="SummaryOfChanges" value="ON" />
		<add key="BriteBillDAPTileToggle" value="ON" />
		<!-- Single Rater-->
		<add key="SINGLE_RATER" value="ON" />
		<add key="IsOneBillMigrationComplete" value="OFF" />

		<add key="FibeDTH_ITCR100_Enabled" value="ON" />
		<add key="Crp_Redesign_Enabled" value="ON"  />
		<add key="IsCRPMVPCCEEnabled" value="ON"  />
		<add key="Crp_Filter_isCoverageToggle" value="ON" />
		<add key="Crp_Filter_isSharingToggle" value="ON" />
		<add key="Crp_Filter_isDataToggle" value="ON" />
		<add key="Crp_PreviewCurrentSolution" value="ON" />
		<add key="Crp_isMRCHigh" value="OFF" />
		<add key="RealTime_Billing" value="ON" />
		<add key="Blue_Horizon_Toggle" value="ON"/>
		<add key="ECHAT_MERGE_COOKIES_TOGGLE" value="ON" />
		<add key="BriteBillPBEToggle" value="ON"/>
		<add key="Crp_Redesign_DAP_Enabled" value="ON"/>

		<!-- ESIM local mock response-->
		<add key="ESIMgetEsimInfoLocalMockEnabled" value="OFF" />
		<add key="ESIMgetEsimConfirmOrderLocalMockEnabled" value="OFF" />
		<add key="ESIMretryEsimConfirmOrderLocalMockEnabled" value="OFF" />
		<add key="ESIMgetEsimRecoveryLocalMockEnabled" value="OFF" />
		<add key="Autopay_Toggle" value="ON"/>
		<add key="EnableOneTimePayment" value="ON"/>

		<!--Bell HUG Redesign 2024-25 -->
		<add key="Bell_Hug_Redesign_Enabled" value="ON"/>
		<add key="Bell_Hug_Redesign_RatePlan_High_To_Low_Enabled" value="OFF"/>
		<add key="Bell_Hug_Redesign_Listing_Enabled" value="ON"/>
		<add key="Bell_Hug_Redesign_Display_RatePlan_First" value="ON"/>
		<add key="COAM_Days_Before_HUG_Restriction_Enabled" value="ON"/>
		<add key="Hug_Redesign_Display_Customers_Brand_First" value="ON"/>

		<add key="EnableOneTimeCreditCardPayment" value ="ON" />

		<!--CIAM MFA Authentication Toggle -->
		<add key="CIAMMFAToggle" value="ON"/>
		<add key="CIAMMFAToggleR5011" value="ON"/>
		<add key="CIAMLogDetailedLoginFlows" value="ON"/>
		<add key="CIAMMFAToggle_MarketLaunch" value="ON"/>
		<add key="CIAMMFAToggle_MarketLaunch_March" value="ON"/>
		<add key="CIAMCustomMFAAlwaysOnToggle" value="OFF"/>
		<!--<add key="EnableOneTimeCreditCardPayment" value ="RESTRICTED" />-->
		<add key="EnableDPPWCOCRP" value="ON" />
		<add key="EnableDPPWCOHUG" value="ON" />
		<add key="EnableBellHugWcoMsgRedesignA" value="OFF" />
		<add key="EnableBellHugWcoMsgRedesignB" value="OFF" />
		<!-- OneTrust Data Privacy Value: ON / OFF -->
		<add key="Data_Privacy_Cookies_Toggle" value="ON" />
		<!--Bell WireLine Contract Toggle: Value ON / OFF-->
		<add key="EnableWLC" value="ON" />
		<!--Bell WireLine Contract MVP-2 Toggle: Value ON / OFF-->
		<add key="EnableWLC_MVP2" value="OFF" />
		<add key="Echat_Central_Toggle" value="google"/>
		<add key="WirelineContractsToggle" value="ON" />
		
		<!-- Consent Management Request enable -->
		<add key="ConsentMgmtWebRequest" value="ON" />

		<add key="Autopay_Ph2C" value="ON" />
		<add key="SetupPreauthorizedWidgetsToggle" value="ON" />
		<add key="ManagePreauthorizedWidgetsToggle" value="ON" />
		<add key="SetupPreauthorizedWidgetsInteracToggle" value="ON"/>
		<add key="SetupPreauthorizedWidgetsSingleClickToggle" value="ON" />
		<add key="SetupPreauthorizedWidgetsAutopayCreditToggle" value="ON" />
		<add key="ManagePreauthorizedWidgetsInteracToggle" value="ON" />
		<add key="ManagePreauthorizedWidgetsAutopayCreditToggle" value="ON" />
		<add key="Katsumi_Toggle" value="PILOT" />
		<add key="IsEsimInOMEnabled" value="ON" />
	</settings>
</Configuration>