<?xml version="1.0"?>
<CustomerProfileResponseType xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <ResponseHeader xmlns="http://bside.int.bell.ca/customer/profile/messages">
    <RequestTimestamp xmlns="http://bside.int.bell.ca/common/messages">2021-06-15T16:16:53.9436378+05:30</RequestTimestamp>
    <ResponseTimestamp xmlns="http://bside.int.bell.ca/common/messages">2021-06-15T16:17:07.915+05:30</ResponseTimestamp>
    <ClientID xmlns="http://bside.int.bell.ca/common/messages">bell</ClientID>
    <ServerID xmlns="http://bside.int.bell.ca/common/messages">CSS-PROD-QC_MS13</ServerID>
    <ServiceVersion xmlns="http://bside.int.bell.ca/common/messages">11.8</ServiceVersion>
    <Status xmlns="http://bside.int.bell.ca/common/messages">Success</Status>
  </ResponseHeader>
  <CustomerProfile xmlns="http://bside.int.bell.ca/customer/profile/messages">
    <ProfileType xmlns="http://bside.int.bell.ca/customer/profile/types">BUP</ProfileType>
    <GUID xmlns="http://bside.int.bell.ca/customer/profile/types">ITOT0D6C2N7LKQ1</GUID>
    <MarketingID xmlns="http://bside.int.bell.ca/customer/profile/types">106213</MarketingID>
    <UserName xmlns="http://bside.int.bell.ca/customer/profile/types">barbstone</UserName>
    <ContactName xmlns="http://bside.int.bell.ca/customer/profile/types">
      <FirstName>Barbara</FirstName>
      <LastName>Stone</LastName>
    </ContactName>
    <EMailAddress xmlns="http://bside.int.bell.ca/customer/profile/types"><EMAIL></EMailAddress>
    <Language xmlns="http://bside.int.bell.ca/customer/profile/types">en</Language>
    <CreatedOn xmlns="http://bside.int.bell.ca/customer/profile/types">2020-06-30T19:36:09Z</CreatedOn>
    <LastLogin xmlns="http://bside.int.bell.ca/customer/profile/types">2021-06-14T16:58:46Z</LastLogin>
    <ModifiedOn xmlns="http://bside.int.bell.ca/customer/profile/types">2021-06-14T16:58:46Z</ModifiedOn>
    <PasswordChangeDate xmlns="http://bside.int.bell.ca/customer/profile/types">2020-09-20T07:14:40</PasswordChangeDate>
    <ProfileUpdateDate xmlns="http://bside.int.bell.ca/customer/profile/types">2021-03-22T09:07:07</ProfileUpdateDate>
    <OnlineMarketingConsentDate xmlns="http://bside.int.bell.ca/customer/profile/types">2099-12-31T05:00:00Z</OnlineMarketingConsentDate>
    <RecoveryMobileNumber xmlns="http://bside.int.bell.ca/customer/profile/types">**********</RecoveryMobileNumber>
    <RecoveryEMailAddress xmlns="http://bside.int.bell.ca/customer/profile/types"><EMAIL></RecoveryEMailAddress>
    <LegacyAccounts xmlns="http://bside.int.bell.ca/customer/profile/types">
      <MobilityAccounts>
        <MobilityAccount>
          <AccountNumber>*********</AccountNumber>
          <ResponseStatus>Found</ResponseStatus>
          <ResponseStatusDescription>GBD: Loaded from Cache | GBS: Loaded from Cache</ResponseStatusDescription>
          <ResponseVersion>65</ResponseVersion>
          <AccountStatus>Active</AccountStatus>
          <AccountStatusReason>RSC</AccountStatusReason>
          <Nickname>*********</Nickname>
          <AccountName>   BARBARA STONE </AccountName>
          <LinkType>BUP</LinkType>
          <AccountType Code="I">Individual</AccountType>
          <AccountSubType Code="I">CorpInternal</AccountSubType>
          <OrganizationID>Bell</OrganizationID>
          <Submarket Code="UOC">UOC</Submarket>
          <IsPrepaid>false</IsPrepaid>
          <ContactAddress>
            <PrimaryLine>5099 CREEKBANK RD APT 3</PrimaryLine>
            <AddressType Code="67">DetailCity</AddressType>
            <StreetName>CREEKBANK</StreetName>
            <StreetType>RD</StreetType>
            <City>MISSISSAUGA</City>
            <Province Code="ON">Ontario</Province>
            <PostalCode>L4W5N2</PostalCode>
            <Country>CAN</Country>
            <CivicNum>5099</CivicNum>
            <DwellingType Code="APT">Apartment</DwellingType>
            <ApartmentNo>3</ApartmentNo>
          </ContactAddress>
          <EMailAddress><EMAIL></EMailAddress>
          <BillingName>   BARBARA STONE </BillingName>
          <BillingAddress>
            <PrimaryLine>5099 CREEKBANK RD APT 3</PrimaryLine>
            <AddressType Code="67">DetailCity</AddressType>
            <StreetName>CREEKBANK</StreetName>
            <StreetType>RD</StreetType>
            <City>MISSISSAUGA</City>
            <Province Code="ON">Ontario</Province>
            <PostalCode>L4W5N2</PostalCode>
            <Country>CAN</Country>
            <CivicNum>5099</CivicNum>
            <DwellingType Code="APT">Apartment</DwellingType>
            <ApartmentNo>3</ApartmentNo>
          </BillingAddress>
          <Language Code="E">en</Language>
          <AccountCommPref Code="78">Unknown</AccountCommPref>
          <IsAccountSMSPerm>false</IsAccountSMSPerm>
          <ARBalance>-5</ARBalance>
          <BillCycleCode>46</BillCycleCode>
          <CurrentCycleEndDate>2021-06-15T09:30:00+05:30</CurrentCycleEndDate>
          <NextCycleStartDate>2021-05-16T09:30:00+05:30</NextCycleStartDate>
          <PaymentMethod Code="R">Regular</PaymentMethod>
          <EBillInfo>
            <IsEBillEnrolled>true</IsEBillEnrolled>
            <IsEBillNotifyEnabled>true</IsEBillNotifyEnabled>
            <EBillStartDate>2019-08-17T09:30:00+05:30</EBillStartDate>
            <EBillEndDate>2030-07-02T09:30:00+05:30</EBillEndDate>
          </EBillInfo>
          <SIOwner Code="MOBL">BellMobility</SIOwner>
          <IsInCollection>false</IsInCollection>
          <SpendingCaps>0</SpendingCaps>
          <SpendingCapsUsed>0</SpendingCapsUsed>
          <LastUpdateDate>2021-06-09T22:29:02+05:30</LastUpdateDate>
          <LastUpdateStamp>4437</LastUpdateStamp>
          <AccountHolder>*********</AccountHolder>
          <ARPUAmount>0.0</ARPUAmount>
          <LogicalCurrentDate>2021-06-14T09:30:00+05:30</LogicalCurrentDate>
          <CreditInfo>
            <CreditClass Code="A">Special</CreditClass>
            <CreditScore>0</CreditScore>
          </CreditInfo>
          <MarketSegment Code="MOB">Mobility</MarketSegment>
          <ConsolidationSystem>None</ConsolidationSystem>
          <IsBillSuppressed>true</IsBillSuppressed>
          <IsEcareRegistered>true</IsEcareRegistered>
          <BillingAddressUpdateDate>2020-07-02T09:30:00+05:30</BillingAddressUpdateDate>
          <HeldDepositeAmount>0</HeldDepositeAmount>
          <VisibilityLevel>Account</VisibilityLevel>
          <Subscribers>
            <Subscriber>
              <SubscriberID>********</SubscriberID>
              <SubscriberStatus>Active</SubscriberStatus>
              <Nickname>**********</Nickname>
              <SubscriberName>BARBARA STONE </SubscriberName>
              <DeviceGroup>NumbrShare</DeviceGroup>
              <ProductType Code="CEL">Cellular</ProductType>
              <Hardware>
                <ModelNumber>QANN16GX</ModelNumber>
                <SIMNumber>89302610203058973948</SIMNumber>
                <IMEI>***************</IMEI>
                <SequenceNumber>*********</SequenceNumber>
                <SIMSequenceNumber>*********</SIMSequenceNumber>
                <IMEIEquipmentType Code="T">LTEDevice</IMEIEquipmentType>
                <SIMEquipmentType Code="U">USimVal</SIMEquipmentType>
              </Hardware>
              <TelephoneNumber>**********</TelephoneNumber>
              <NetworkType Code="85">UMTS</NetworkType>
              <Language Code="E">en</Language>
              <IsBillSixty>false</IsBillSixty>
              <IsTab>false</IsTab>
              <CommitmentTerm>0</CommitmentTerm>
              <PaccPinStatus Code="78">NotEnrolled</PaccPinStatus>
              <PadPinStatus Code="78">NotEnrolled</PadPinStatus>
              <InitialActivationDate>2018-12-08T10:30:00+05:30</InitialActivationDate>
              <IsAccountSMSPerm>false</IsAccountSMSPerm>
              <LastUpdateDate>2021-06-09T22:29:02+05:30</LastUpdateDate>
              <LastUpdateStamp>4430</LastUpdateStamp>
              <SubscriberEstablishDate>2018-12-08T10:30:00+05:30</SubscriberEstablishDate>
              <DaysSinceActivation>919</DaysSinceActivation>
              <NextTopupDate>2021-07-07T09:30:00+05:30</NextTopupDate>
              <InitialPassword>6017</InitialPassword>
              <IsCallDisplayAllowed>false</IsCallDisplayAllowed>
              <PricePlan>WTSINL30</PricePlan>
              <PrimeMateInidicator Code="R">Unknown</PrimeMateInidicator>
              <SubMarket Code="UOC">UOC</SubMarket>
              <TelcoId>MOBL</TelcoId>
              <PinUnlockKey>********</PinUnlockKey>
              <PinUnlockKey>2679983</PinUnlockKey>
              <ManitobaIndicator>O</ManitobaIndicator>
              <ThunderBayIndicator>O</ThunderBayIndicator>
              <PortabilityIndicator>O</PortabilityIndicator>
              <ServiceArea>N</ServiceArea>
              <HasOrderInProgress>false</HasOrderInProgress>
              <IsWCoCSubscriber>false</IsWCoCSubscriber>
              <HasDomesticDataServices>false</HasDomesticDataServices>
              <HasRoamingDataServices>false</HasRoamingDataServices>
              <IsAccessible>false</IsAccessible>
              <DeferredAmount>0</DeferredAmount>
              <AssociatedMDNs>
                <SecondaryMDN />
              </AssociatedMDNs>
            </Subscriber>
            <Subscriber>
              <SubscriberID>58115969</SubscriberID>
              <SubscriberStatus>Active</SubscriberStatus>
              <Nickname>**********</Nickname>
              <SubscriberName>BARB STONE </SubscriberName>
              <ProductType Code="CEL">Cellular</ProductType>
              <Hardware>
                <SIMNumber>89302610103033358993</SIMNumber>
                <IMEI>356761050104865</IMEI>
                <SequenceNumber>216935132</SequenceNumber>
                <SIMSequenceNumber>217774718</SIMSequenceNumber>
                <IMEIEquipmentType Code="H">IMEI</IMEIEquipmentType>
                <SIMEquipmentType Code="U">USimVal</SIMEquipmentType>
              </Hardware>
              <TelephoneNumber>**********</TelephoneNumber>
              <NetworkType Code="85">UMTS</NetworkType>
              <Language Code="E">en</Language>
              <IsBillSixty>false</IsBillSixty>
              <IsTab>false</IsTab>
              <CommitmentTerm>0</CommitmentTerm>
              <PaccPinStatus Code="78">NotEnrolled</PaccPinStatus>
              <PadPinStatus Code="78">NotEnrolled</PadPinStatus>
              <InitialActivationDate>2005-03-14T10:30:00+05:30</InitialActivationDate>
              <AccountCommPref Code="78">Unknown</AccountCommPref>
              <IsAccountSMSPerm>false</IsAccountSMSPerm>
              <LastUpdateDate>2021-06-09T22:28:31+05:30</LastUpdateDate>
              <LastUpdateStamp>5664</LastUpdateStamp>
              <SubscriberEstablishDate>1998-03-25T10:30:00+05:30</SubscriberEstablishDate>
              <DaysSinceActivation>8482</DaysSinceActivation>
              <NextTopupDate>2021-07-13T09:30:00+05:30</NextTopupDate>
              <InitialPassword>2935</InitialPassword>
              <IsCallDisplayAllowed>false</IsCallDisplayAllowed>
              <PricePlan>INTL5</PricePlan>
              <PrimeMateInidicator Code="R">Unknown</PrimeMateInidicator>
              <SubMarket Code="UOC">UOC</SubMarket>
              <TelcoId>MOBL</TelcoId>
              <PinUnlockKey>********</PinUnlockKey>
              <PinUnlockKey>********</PinUnlockKey>
              <ManitobaIndicator>O</ManitobaIndicator>
              <ThunderBayIndicator>O</ThunderBayIndicator>
              <PortabilityIndicator>O</PortabilityIndicator>
              <ServiceArea>N</ServiceArea>
              <HasOrderInProgress>false</HasOrderInProgress>
              <IsWCoCSubscriber>false</IsWCoCSubscriber>
              <HasDomesticDataServices>false</HasDomesticDataServices>
              <HasRoamingDataServices>false</HasRoamingDataServices>
              <IsAccessible>false</IsAccessible>
              <DeferredAmount>0</DeferredAmount>
              <InternetV2Number>1583</InternetV2Number>
              <AssociatedMDNs>
                <SecondaryMDN />
              </AssociatedMDNs>
            </Subscriber>
          </Subscribers>
        </MobilityAccount>
      </MobilityAccounts>
    </LegacyAccounts>
	<NM1Accounts xmlns="http://bside.int.bell.ca/customer/profile/types">
               <NM1Account>
                  <AccountNumber>*********</AccountNumber>
                  <ResponseStatus>Found</ResponseStatus>
                  <ResponseVersion>61</ResponseVersion>
                  <AccountStatus>Active</AccountStatus>
                  <AccountStatusReason>NAC</AccountStatusReason>
                  <Nickname>*********</Nickname>
                  <AccountName>ANDR  LARIV</AccountName>
                  <LinkType>BUP</LinkType>
                  <AccountType Code="I">Individual</AccountType>
                  <AccountSubType Code="V">TMIDivision</AccountSubType>
                  <OrganizationID>Virgin</OrganizationID>
                  <Submarket Code="110">Unknown</Submarket>
                  <IsPrepaid>false</IsPrepaid>
                  <ContactAddress>
                     <PrimaryLine>598 STE ANNE DES LACS</PrimaryLine>
                     <AddressType Code="68">Rural</AddressType>
                     <StreetName>STE ANNE DES LACS</StreetName>
                     <City>SAINTE-ANNE-DES-LACS</City>
                     <Province Code="QC">Quebec</Province>
                     <PostalCode>J0R1B0</PostalCode>
                     <Country>CAN</Country>
                     <CivicNum>598</CivicNum>
                  </ContactAddress>
                  <BillingName>ANDR  LARIV</BillingName>
                  <BillingAddress>
                     <PrimaryLine>598 STE ANNE DES LACS</PrimaryLine>
                     <AddressType Code="68">Rural</AddressType>
                     <StreetName>STE ANNE DES LACS</StreetName>
                     <City>SAINTE-ANNE-DES-LACS</City>
                     <Province Code="QC">Quebec</Province>
                     <PostalCode>J0R1B0</PostalCode>
                     <Country>CAN</Country>
                     <CivicNum>598</CivicNum>
                  </BillingAddress>
                  <Language Code="F">fr</Language>
                  <IsAccountSMSPerm>true</IsAccountSMSPerm>
                  <ARBalance>63.24</ARBalance>
                  <BillCycleCode>31</BillCycleCode>
                  <CurrentCycleEndDate>2020-02-29T00:00:00.000-05:00</CurrentCycleEndDate>
                  <NextCycleStartDate>2020-02-01T00:00:00.000-05:00</NextCycleStartDate>
                  <PaymentMethod Code="R">Regular</PaymentMethod>
                  <EBillInfo>
                     <IsEBillEnrolled>false</IsEBillEnrolled>
                     <IsEBillNotifyEnabled>false</IsEBillNotifyEnabled>
                  </EBillInfo>
                  <SIOwner Code="MOBL">BellMobility</SIOwner>
                  <IsInCollection>false</IsInCollection>
                  <SpendingCaps>0.0</SpendingCaps>
                  <SpendingCapsUsed>0.0</SpendingCapsUsed>
                  <LastUpdateDate>2020-02-01T09:12:38.000-05:00</LastUpdateDate>
                  <LastUpdateStamp>7667</LastUpdateStamp>
                  <AccountHolder>**********</AccountHolder>
                  <ARPUAmount>55.0</ARPUAmount>
                  <LogicalCurrentDate>2020-02-06T00:00:00.000-05:00</LogicalCurrentDate>
                  <CreditInfo>
                     <CreditClass Code="D">Poor</CreditClass>
                     <CreditScore>0</CreditScore>
                  </CreditInfo>
                  <MarketSegment Code="PRS">Prestige</MarketSegment>
                  <ConsolidationSystem>None</ConsolidationSystem>
                  <IsBillSuppressed>false</IsBillSuppressed>
                  <IsEcareRegistered>false</IsEcareRegistered>
                  <BillingAddressUpdateDate>2020-01-27T00:00:00.000-05:00</BillingAddressUpdateDate>
                  <HeldDepositeAmount>0.0</HeldDepositeAmount>
                  <ContractDistributionInfo>
                     <Language Code="F">fr</Language>
                     <ContrDistributionMedia Code="NORM">Normal</ContrDistributionMedia>
                     <ContrPreference>PAPR</ContrPreference>
                  </ContractDistributionInfo>
                  <VisibilityLevel>Account</VisibilityLevel>
                  <Subscribers>
				  	<InternetSubscriber>
						<SubscriberID>********</SubscriberID>
						<SubscriberStatus>Active</SubscriberStatus>
						<LOBAccountNumber>b1oojr33</LOBAccountNumber>
						<Nickname>**********</Nickname>
						<SubscriberName>Mr BQAT TESTING </SubscriberName>
						<ProductType Code="AFC">AirFree</ProductType>
						<Hardware>
							<SequenceNumber>0.0</SequenceNumber>
							<SIMSequenceNumber>0.0</SIMSequenceNumber>
						</Hardware>
						<TelephoneNumber>**********</TelephoneNumber>
						<Language Code="E">en</Language>
						<IsBillSixty>false</IsBillSixty>
						<IsTab>false</IsTab>
						<CommitmentTerm>0</CommitmentTerm>
						<PaccPinStatus Code="78">NotEnrolled</PaccPinStatus>
						<PadPinStatus Code="78">NotEnrolled</PadPinStatus>
						<InitialActivationDate>2020-03-03T00:00:00.000-05:00</InitialActivationDate>
						<IsAccountSMSPerm>false</IsAccountSMSPerm>
						<LastUpdateDate>2020-11-05T13:08:04.000-05:00</LastUpdateDate>
						<LastUpdateStamp>7933</LastUpdateStamp>
						<SubscriberEstablishDate>2020-03-03T00:00:00.000-05:00</SubscriberEstablishDate>
						<DaysSinceActivation>427</DaysSinceActivation>
						<NextTopupDate>2021-06-02T00:00:00.000-04:00</NextTopupDate>
						<IsCallDisplayAllowed>false</IsCallDisplayAllowed>
						<PricePlan>ZZINTOUH4</PricePlan>
						<PrimeMateInidicator Code="R">Unknown</PrimeMateInidicator>
						<SubMarket Code="100">Unknown</SubMarket>
						<TelcoId>MOBL</TelcoId>
						<ManitobaIndicator>O</ManitobaIndicator>
						<ThunderBayIndicator>O</ThunderBayIndicator>
						<PortabilityIndicator>O</PortabilityIndicator>
						<ServiceArea>N</ServiceArea>
						<HasOrderInProgress>false</HasOrderInProgress>
						<IsWCoCSubscriber>false</IsWCoCSubscriber>
						<HasDomesticDataServices>false</HasDomesticDataServices>
						<HasRoamingDataServices>false</HasRoamingDataServices>
						<IsAccessible>false</IsAccessible>
						<DeferredAmount>0.0</DeferredAmount>
						<InternetV2Number>v2aunr84</InternetV2Number>
						<AssociatedMDNs>
							<SecondaryMDN/>
						</AssociatedMDNs>
					</InternetSubscriber>
                     <TVSubscriber>
                        <SubscriberID>********</SubscriberID>
                        <SubscriberStatus>Active</SubscriberStatus>
                        <LOBAccountNumber>****************</LOBAccountNumber>
				        <Technology>DTH</Technology>
                        <Nickname>tv-nick</Nickname>
                        <SubscriberName>ANDR LARIV</SubscriberName>
                        <ProductType Code="RTV">BellTV</ProductType>
                        <NetworkType Code="79">DTH</NetworkType>
                        <Language Code="F">fr</Language>
                        <IsBillSixty>false</IsBillSixty>
                        <IsTab>false</IsTab>
                        <CommitmentTerm>0</CommitmentTerm>
                        <PaccPinStatus Code="78">NotEnrolled</PaccPinStatus>
                        <PadPinStatus Code="78">NotEnrolled</PadPinStatus>
                        <InitialActivationDate>2020-01-28T00:00:00.000-05:00</InitialActivationDate>
                        <IsAccountSMSPerm>false</IsAccountSMSPerm>
                        <LastUpdateDate>2020-01-28T07:16:21.000-05:00</LastUpdateDate>
                        <LastUpdateStamp>3045</LastUpdateStamp>
                        <SubscriberEstablishDate>2020-01-28T00:00:00.000-05:00</SubscriberEstablishDate>
                        <DaysSinceActivation>9</DaysSinceActivation>
                        <NextTopupDate>2020-06-27T00:00:00.000-05:00</NextTopupDate>
                        <IsCallDisplayAllowed>false</IsCallDisplayAllowed>
                        <PricePlan>RTVALT01</PricePlan>
                        <PrimeMateInidicator Code="R">Unknown</PrimeMateInidicator>
                        <SubMarket Code="ION">ION</SubMarket>
                        <TelcoId>BRST</TelcoId>
                        <ManitobaIndicator>O</ManitobaIndicator>
                        <ThunderBayIndicator>O</ThunderBayIndicator>
                        <PortabilityIndicator>O</PortabilityIndicator>
                        <ServiceArea>N</ServiceArea>
                        <HasOrderInProgress>false</HasOrderInProgress>
                        <IsWCoCSubscriber>false</IsWCoCSubscriber>
                        <HasDomesticDataServices>false</HasDomesticDataServices>
                        <HasRoamingDataServices>false</HasRoamingDataServices>
                        <IsAccessible>false</IsAccessible>
                        <DeferredAmount>0.0</DeferredAmount>
                     </TVSubscriber>
                  </Subscribers>
               </NM1Account>
		</NM1Accounts>
    <Nicknames xmlns="http://bside.int.bell.ca/customer/profile/types">
      <Nickname TypeId="3" Type="Wireline" AccNo="**********">Home phone</Nickname>
      <Nickname TypeId="6" Type="Device" AccNo="********">Kevin</Nickname>
      <Nickname TypeId="6" Type="Device" AccNo="********">Mum</Nickname>
      <Nickname TypeId="6" Type="Device" AccNo="********">Dad</Nickname>
      <Nickname TypeId="6" Type="Device" AccNo="********">Mom Stone Ipad</Nickname>
      <Nickname TypeId="6" Type="Device" AccNo="********">Gary</Nickname>
    </Nicknames>
  </CustomerProfile>
</CustomerProfileResponseType>