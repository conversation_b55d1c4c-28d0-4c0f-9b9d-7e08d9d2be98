<?xml version="1.0" encoding="utf-8"?>
<Configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <settings>
    <add key="TVCSToggle" value="ON" />
    <add key="TVCSToggleMBM" value="ON" />
    <add key="TVCSChangeProgrammingToggle" value="ON" />
    <add key="TVCSChangeProgrammingToggleMBM" value="ON" />
    <add key="TVChangeProgrammingBlockingPeriod" value="137" />
    <add key="Nm1TVChangeProgrammingBlockingPeriod" value="120" />
    <add key="TVCSBaseFilterDefaults" value="['Family', 'Movies', 'News', 'News and Learning', 'Sports', 'Variety']" />
    <add key="TVCSVisibleTopPicksIconsInCombos" value="3" />
    <add key="TVCSVisibleTopPicksIconsInPackages" value="12" />
    <add key="TVCSNumberOfCharactersToStartSearchInMenu" value="1" />
    <add key="TVCSNumberOfCharactersToStartSearchInPage" value="2" />
    <add key="TVCSNumberOfSuggestionsInSearchPage" value="10" />
    <add key="TVCSNumberOfSuggestionsInSearchMenu" value="15" />
    <add key="numberOfChannelsToShowInAllResults" value="12" />
    <add key="numberOfShowsToShowInAllResults" value="12" />
    <add key="TVCSKeepAliveInterval" value="600000" />
    <!--10 minutes between keep alive calls in PPV and TVCS step1 and step 2 -->
    <add key="Bell.Ordering.Api.DeleteSelection.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Orders/{1}/Selection/Delete?province={2}" />
    <add key="Bell.Ordering.Api.GetSelection.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Orders/{1}/Selection?province={2}" />
    <add key="Bell.Ordering.Api.SaveSelection.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Orders/{1}/Selection/Save?province={2}" />
    <add key="Bell.Ordering.Api.OrderId.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Orders?province={1}" />
    <add key="Bell.Ordering.Api.Brochure.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Brochure" />
    <add key="Bell.Ordering.Api.Review.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Orders/{1}/Review" />
    <add key="Bell.Ordering.Api.Confirmation.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Orders/{1}/Submit" />
    <add key="Bell.Ordering.Api.Migration.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Orders/{1}/Migration?province={2}" />
    <add key="Bell.Ordering.Api.Search.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Shows/Search" />
    <add key="Bell.Ordering.Api.KeepAlive.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Cache/Keepalive?province={1}" />
    <add key="Bell.Ordering.Api.PopularShows.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Shows" />
    <add key="Bell.Catalog.Api.GetPrefetch.Path" value="/UXP.Services/ecare/Catalog/TV/Prefetch/Catalog" />
    <add key="Bell.EPG.Api.GetPrefetch.Path" value="/UXP.Services/ecare/EPG/Prefetch/EPG" />
    <add key="Bell.MDM.Api.GetPrefetch.Path" value="/UXP.Services/ecare/MDM/Prefetch" />
    <add key="Bell.ServiceAccount.Api.TVOverview" value="Overview?serviceType={0}&amp;province={1}" />
    <add key="Bell.ServiceAccount.Api.TVEquipment" value="Equipment?serviceType={0}&amp;province={1}" />
    <add key="Bell.ServiceAccount.Api.TVLineupDetail" value="Lineup?province={0}" />
    <add key="Bell.ServiceAccount.Api.Prefetch.TVCustomerOrder" value="Prefetch/CustomerOrder?serviceType={0}&amp;province={1}" />
    <add key="Bell.ServiceAccount.Api.MSP.Path" value="GetMultiServicePromo?province={0}" />
    <add key="Bell.Usage.Api.TVUsageDetails" value="Details?province={0}" />
    <add key="Bell.Support.Api.SupportArticle" value="SupportArticle?province={0}" />
    <add key="Bell.CustomerProfile.Api.GetPrefetch.Path" value="/UXP.Services/ecare/CustomerProfile/CustomerAccounts/{0}/Prefetch/GetUserProfile" />
    <add key="Bell.CustomerBRSDetails.Api.GetPrefetch.Path" value="/UXP.Services/ecare/CustomerProfile/CustomerAccounts/{0}/Prefetch/CustomerBRSDetails/{1}?lob=TV" />
    <add key="Bell.Billing.Api.OneBillOverview" value="Overview?province={0}" />
    <add key="Bell.Ordering.Api.Cancel.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Brochure/Reset?serviceType={1}&amp;province={2}&amp;orderId={3}" />
    <add key="Bell.Ordering.Api.RefreshCache.Path" value="/UXP.Services/ecare/Ordering/TV/{0}/Cache/Refresh?province={1}" />
    <add key="tvcsPhaseTwoToggle" value="ON" />
    <add key="Phoenix_Receiver_9500_Identifier" value="hdpvr_x033402" />
    <add key="Receiver_7500_Identifier" value="wholehom_x039802" />
    <add key="Receiver_6400_Identifier" value="hdreceiv_x0201" />
    
    <add key="FMO_Phoenix_Receiver_9500_Identifier" value="wholehom_x056205" />
    <add key="FMO_Receiver_7500_Identifier" value="wholehom_x056206" />
    <add key="FMO_Receiver_6400_Identifier" value="hdreceiv_x056201" />
    <add key="FMO_Phoenix_Receiver_9500_Identifier_Equipment" value="wholehom_x056211" />
    <add key="FMO_Receiver_7500_Identifier_Equipment" value="wholehom_x056212" />
    <add key="FMO_Receiver_6400_Identifier_Equipment" value="hdreceiv_x056207" />
    <!-- <add key="BlOCK_SD_RECEIVERS" value="S994976877500,S994976877400,S994976862500,S994976862400,S994977021500,S994977021400,S994977021300,S994977021200,S994976887500,S994976887400,S994976897500,S994976897400,S994976907500,S994976907400,S994977006300,S994977006400,S994976917500,S994976917400,S994976927500,S994976927400,S994976935400,S994976935300,S994976952500,S994976952400,S994976837500,S994976837400,S994977037500,S994977037400"/> -->
    <add key="BlOCK_SD_RECEIVERS" value="SDReceivers"/>
    <add key="Identifier_9500" value="whpvr_x033402a030001v032201" />
    <add key="ParentIdentifier_7500" value="wholehom_x039802" />
    <add key="HardwarePriceIncreaseStarteDate" value="12-15-2018"/>
    <add key="HardwarePriceIncreaseEndDate" value="03-01-2019"/>
    <add key="AUDITCONFIRMATIONEMAILEVENTTYPE" value="AuditReceivers"/>
    <add key="MIGRATIONCONFIRMATIONEMAILEVENTTYPE" value="MigrateReceivers"/>
    <!-- Ordering API -->
    <add key="Bell.TVProgramming.Api.Base" value="UXP.Services/ecare/TVProgramming/" />
    <add key="Bell.ServiceAccount.Api.Base" value="UXP.Services/ecare/Serviceaccount/TV/" />
    <add key="Bell.CustomerProfile.Api.Base" value="UXP.Services/ecare/CustomerProfile/" />
    <add key="Bell.EPG.Api.Base" value="UXP.Services/ecare/EPG/" />
    <add key="Bell.Catalog.Api.Base" value="UXP.Services/ecare/Catalog/" />
    <add key="Bell.Catalog.Api.GetPPVAssetDetail.Path" value="TV/PPV/{0}/Detail?serviceType={1}&amp;serviceAccountId={2}&amp;province={3}" />
    <add key="Bell.TVProgramming.Api.Prefetch.PersonlizeBrochure.Path" value="{0}/Prefetech/ProgrammingDetails?province={1}" />
    <add key="Bell.TVProgramming.Api.SubmitOrder.Path" value="{0}/Submit?province={1}" />
    <add key="Bell.TVProgramming.Api.GetTVMigrationDetails.Path" value="{0}/Migration?province={1}" />
    <add key="Bell.TVProgramming.Api.RefreshShoppingCart.Path" value="{0}/ShoppingCart/Refresh?province={1}" />
    <add key="Bell.TVProgramming.Api.RefreshApiCache.Path" value="{0}/Cache/Refresh?province={1}" />
    <add key="Bell.TVProgramming.Api.ProcessSavedSelection.Path" value="{0}/Selection?province={1}" />
    <add key="Bell.TVProgramming.Api.GetOrderId.Path" value="{0}/Order?province={1}&amp;refreshCache={2}" />
    <add key="Bell.TVProgramming.Api.GetCurrentProgramming.Path" value="{0}/CurrentProgramming?province={1}&amp;refreshCache={2}" />
    <add key="Bell.TVProgramming.Api.GetShoppingCart.Path" value="{0}/ShoppingCart?province={1}" />
    <add key="Bell.TVProgramming.Api.ChangeOfferingSelection.Path" value="{0}/Offering?province={1}" />
    <add key="Bell.TVProgramming.Api.NewSectionPrice.Path" value="{0}/Brochure/OfferingPrice?province={1}" />
    <add key="Bell.TVProgramming.Api.GetReviewNotification.Path" value="{0}/Review/Notification?province={1}" />
    <add key="Bell.TVProgramming.Api.GetTVProgrammingDetails.Path" value="{0}/ProgrammingDetails?province={1}&amp;refreshCache={2}" />
    <add key="Bell.TVProgramming.Api.KeepCacheAlive.Path" value="{0}/Cache/Keepalive?province={1}" />
    <add key="Bell.ServiceAccount.Api.GetEquipment.Path" value="{0}/Equipment?serviceType={1}&amp;province={2}" />
    <add key="Bell.ServiceAccount.Api.GetLineupDetail.Path" value="{0}/Lineup?refreshCache={1}&amp;province={2}" />
    <add key="Bell.ServiceAccount.Api.GetTVOverview.Path" value="{0}/Overview?serviceType={1}&amp;refreshCache={2}&amp;province={3}"/>
    <add key="Bell.CustomerProfile.Api.GetUserProfile.Path" value="CustomerAccounts/{0}/CustomerProfile?province={1}" />
    <add key="Bell.CustomerProfile.Api.GetCustomerBRSDetails.Path" value="CustomerAccounts/{0}/CustomerBRSDetails/{1}?lob={2}&amp;province={3}&amp;refreshCache={4}" />
    <add key="Favorite.Channels.Restriction.Count" value="15" />
    <add key="Alacarte.Channels.Limit" value="40" />
    <add key="TV.AdultProgram.Description" value ="Softcore Erotica"/>
    <add key="Starter.AlacarteChannels.Limit" value="40" />
    <add key="Payment.TransactionID_Min" value="1111111"/>
    <add key="Payment.TransactionID_Max" value="9999999"/>
    <add key="Bell.EPG.Api.GetProgramSearchResult.Path" value="Search/Program?searchKey={0}&amp;province={1}" />
    <add key="TMS.Api.GetProgramTMSData.Path" value="v1/programs/{0}?api_key=mwr43qxkvcttzb6md7e8dpr9" />
    <add key="Tms.Image.Base.Path" value ="https://www.tmsimg.com/"/>
    <add key="TV.ChannelLogoPath.Rep" value="https://mybell.bell.ca/styles/tv/channels/logos"/>
    <add key="TV.ChannelLogoPath.xml" value="https://www.bell.ca"/>
    <add key="TvProgrammingMenuFilePath" value="~/Content/GlobalResources/TvProgrammingMenu.xml" />
    <!--TvProgramming-->
    <add key="Bell.ServiceAccount.MultiServicePromo.Path" value="{0}/GetMultiServicePromo?province={1}" />
    <add key="WaitIntervalBetweenRetries" value="1000" />
    <add key="NoOfRetries" value="15" />
    <!--Catalog-->
    <add key="PPVFilePath" value="~/Content/Television/PPV/PPV.xml" />
    <add key="VODFilePath" value="~/Content/Television/VOD/VOD.xml" />
    <add key="NumberOfMoviesOnPPVCorrousel" value="6"/>
    <add key="NumberOfEventsOnPPVCorrousel" value="2"/>
    <add key="NumberOfMoviesOnVODCorrousel" value="8"/>
    <add key="PPVVODGenericImage" value="https://mybell.bell.ca/Resource/web/common/all_languages/all_regions/images/moviePlaceholder.jpg" />
    <add key="Bell.Backchannel.Api.Base" value="http://local-uxp.int.bell.ca/" />
    <add key="Bell.TVProgramming.Api.CatalogGetCurrentProgramming.Path" value="{0}/CurrentProgramming?province={1}" />
    <add key="Bell.EPG.Api.GetSchedule.Path" value="Schedule?province={0}" />
    <add key="Bell.CustomerProfile.Api.CatalogGetCustomerBRSDetails.Path" value="CustomerAccounts/{0}/CustomerBRSDetails/{1}?lob={2}&amp;province={3}" />
    <!--Service Account -->
    <add key="Bell.Order.Api.Base" value="UXP.Services/ecare/Ordering/"/>
    <add key="Bell.TVProgramming.Api.GetTVProgrammingDetails.LineUp.Path" value="{0}/Lineup/Channel?province={1}" />
    <add key="Bell.Tv.Api.Brochure.Path" value="{0}/ProgrammingDetails?province={1}&amp;refreshCache={2}" />
    <add key="Bell.MDM.Api.LoadProduct.Path" value="Products/{0}?province={1}" />
    <add key="Bell.MDM.Api.GetElementsByCategory.Path" value="ElementsByCategory/{0}?province={1}" />
    <add key="Bell.MDM.Api.Base" value="UXP.Services/ecare/MDM/"/>
    <add key="Bell.Order.Api.PrefetchShowSuggestions.Path" value="TV/{0}/Prefetch/ShowSuggestions?province={1}&amp;brochureId={2}" />
    <add key="Bell.Order.Api.GetSelection.Path" value="TV/{0}/Orders/Selection/Get?province={1}"/>
    <add key="TV.ChannelLogoPath" value="https://mybell.bell.ca/styles/tv/channels/logos"/>
    <add key="Update_SmartCardValue" value="S9934,S044,S0455,S0456,S0457,S0458,S06,S22,S0459,S103,S994,S995,S0097,S996"/>
    <add key="FourKStreamingEnabled" value="true" />
    <add key="Search.Top.Show.Count" value="50" />
    <add key="Alacarte.Channel.Count" value="10"/>
    <add key="TVCSPriceAlertMessageToggle" value="ON" />
    <add key="Movies.Combo.Default.Image" value="/Styles/RSX/shop/img/GenericPoster_Movie_Desktop_2x.png"/>
    <add key="Sports.Combo.Default.Image" value="/Styles/RSX/shop/img/tv-tile.jpg" />

    <add key="Bell.Image.Host.Path" value="/styles/tv/channels/logos/"/>

    <add key="Bell.Product.Order.Path" value="/productOrder"/>
    <add key="Bell.Product.Offering.Price.Path" value="/productOfferingPrice"/>
    <add key="Bell.Product.Offering.Path" value="/productOffering"/>
    <add key="Bell.Category.Path" value="/category"/>
    <add key="Bell.DOF.ValidatePromo.Path" value="/validatePromoCode"/>
    <add key="Bell.ServiceAccount.Api.TvEligibility" value="Eligibility?serviceType={0}&amp;province={1}" />

    <add key="BASE_PACKAGE" value="UIBASEPACKAGE_N10226,UIPACKAGES_N10214,UIBASEPACKAGE_N10002,UIPACKAGES_N10040,UIBASEPACKAGE_N10012,UIBASEPACKAGE_N10206,UIBASEPACKAGE_N10006,UIPACKAGES_N10068"/>
    <add key="A_LA_CARTE" value="UIALACARTE_N10245,UIALACARTE_N10064,UIALACARTE_N10051,UIALACARTE_N10023,UIALACARTE_N10091,UIALACARTE_N10031"/>
    <add key="INTERNATIONAL" value="UIINTERNATIONA_N10210,UIINTERNATIONA_N10273,UIINTERNATIONA_N10057,UIINTERNATIONA_N10027,UIINTERNATIONA_N10054"/>
    <add key="MOVIES" value="UIMOVIESANDSER_N10212,UIMOVIESANDSER_N10252,UIMOVIESANDSER_N10041,UIMOVIESANDSER_N10019,UIMOVIESANDSER_N10033"/>
    <add key="SPORTS" value="UISPORTS_N10218,UISPORTS_N10259,UISPORTS_N10046,UISPORTS_N10021,UISPORTS_N10037"/>
    <add key="International_Non_Discounted_Price" value="UIINTERNATIONA_N10238,UIINTERNATIONA_N10088,UIINTERNATIONA_N10077"/>
    <add key="International_a_la_carte" value="UIINTERNATIONA_N10241,UIINTERNATIONA_N10281,UIINTERNATIONA_N10087,UIINTERNATIONA_N10074,UIINTERNATIONA_N10067"/>
    <add key="International_Add_Ons" value="UIINTERNATIONA_N10243,UIINTERNATIONA_N10084,UIINTERNATIONA_N10070"/>
    <add key="International_Combos" value="UIINTERNATIONA_N10247,UIINTERNATIONA_N10278,UIINTERNATIONA_N10081,UIINTERNATIONA_N10065,UIINTERNATIONA_N10060"/>
    <add key="Monthly_Sports" value="UIMONTHLYSPORT_N10250,UIMONTHLYSPORT_N10265,UIMONTHLYSPORT_N10075,UIMONTHLYSPORT_N10056,UIMONTHLYSPORT_N10044"/>
    <add key="Seasonal_Sports" value="UIPREMIUMSPORT_N10254,UIPREMIUMSPORT_N10269,UIPREMIUMSPORT_N10078,UIPREMIUMSPORT_N10062,UIPREMIUMSPORT_N10049"/>
    <!--<add key="Top_picks_packs" value=""/>-->
    <!--<add key="INDIVIDUAL_CHANNELS" value="UIADDITIONALCH_N10011,UIADDITIONALCH_N10014"/>-->
    <add key="HD_THEME_PACKS" value="UIHDTHEMEPACKS_N10208,UIHDTHEMEPACKS_N10233,UIHDTHEMEPACKS_N10072,UIHDTHEMEPACKS_N10035,UIHDTHEMEPACKS_N10052,UIHDTHEMEPACKS_N10016"/>
    <add key="OTHER" value="UISPECIALITYAN_N10216,UIADDONPACKS_N10802,UISPECIALITYAN_N10061,UISPECIALITYAN_N10029"/>
    <add key="THEME_PACKS" value="UITHEMEPACKS_N10220,UITHEMEPACKS_N10261,UITHEMEPACKS_N10071,UITHEMEPACKS_N10004,UITHEMEPACKS_N10047,UITHEMEPACKS_N10008"/>
    <add key="Add_on_packs" value="UIADDONPACKS_N10228,UIADDONPACKS_N10236,UIADDONPACKS_N10095,UIADDONPACKS_N10080,UIADDONPACKS_N10026"/>
    <add key="Additional_Programming_packages" value="UIADDITIONALPR_N10099,UIADDITIONALPR_N10086"/>
    <add key="Specialty_channels" value="UISPECIALTYCHA_N10256,UISPECIALTYCHA_N10096,UISPECIALTYCHA_N10083"/>
    <add key="HD_US_Time_Shifting" value="UIHDTIMESHIFT_N10094"/>
    <add key="HDTV" value="UIHDTV_N10093"/>
    <add key="Mature" value="UIMATURE_N10097,UIADULT_N10604,UIADULT_N10602,UIADULT_N10603"/>
    <add key="On_Demand" value="UIONDEMAND_N10098"/>
    <add key="US_Networks_Time_Shifting" value="UITIMESHIFT_N10092"/>

    <add key="ThemePacks.Genesis.IPTV" value="1=10,2=20,3=21,4=28,5=35,6=30,7=35,8=40,9=45,10=50,11=55,12=60,13=65,14=70,15=75,16=80,17=85,18=90,19=95,20=100,21=105,22=110"/>
    <add key="ThemePacks.Genesis.DTH.QC" value="1=10,2=20,3=20,4=25,5=27,6=30,7=35,8=40,9=41,10=45,11=45,12=50,13=55,14=60,15=65,16=68,17=71,18=76,19=81,20=86,21=90,22=96"/>
    <add key="ThemePacks.Genesis.DTH" value="1=10,2=20,3=20,4=25,5=27,6=30,7=35,8=40,9=41,10=45,11=45,12=50,13=55,14=60,15=65,16=68,17=71,18=76,19=81,20=86,21=91"/>
    <add key="TV.Performance.CheckStatusInterval" value="5000" />
    <add key="TV.Performance.CheckStatusMaxRtry" value="18" />
    <add key="TV.Receivers.SelfInstallReceiverThreshold" value="6" />
    <add key="TV.Receivers.TotalReceiverThreshold" value="10" />
    <add key="TV.Receivers.BellInstallCharges" value="75" />
    <add key="TV.Receivers.BellInstallAdditionalCharges" value="50" />
    <add key="TV.Receivers.DARKTV" value="darktv,zerorate" />
	<add key="CMO_FIBE_TV_BOX_TO_EXCLUDE" value="android4_i041001" />
	<add key="FMO_FIBE_TV_BOX_TO_EXCLUDE" value="android4_i067001,android4_i067201" />
  </settings>
</Configuration>
