@model eCare.Bell.UI.Common.Models.PreAuthorizedPayment
@using System.Configuration;
@using eCare.Common.Context
@using Bell.ca.Framework.Common;
@using eCare.Bell.UI.Common.Extensions;
@using Bell.ca.CustomerInfo.Facade.Managers;
@using System.Web.Optimization;
@using eCare.Bell.UI.Common.Helpers;
@using System.Web.Security.AntiXss;
@using eCare.Bell.UI.Common.Models;
@using Newtonsoft.Json;
@using eCare.Bell.UI.Common.Managers;
@using eCare.Bell.UI.Common.eCareController;
@using Bell.ca.CustomerInfo.Common.Enums.Payment;


@{
    // MVC Variables
    string lang = "en";
    if (Request.Params["language"] != null)
    {
        lang = Request.Params["language"] as string;
    }
    else if (Request.Params["lang"] != null)
    {
        lang = Request.Params["lang"] as string;
    }
    else
    {
        lang = "en";
    }
    string CSRFToken = "";
    var appId = ConfigurationManager.AppSettings.AllKeys.Contains("PCI.ApplicationID") && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["PCI.ApplicationID"]) ? ConfigurationManager.AppSettings["PCI.ApplicationID"] : "";
    var consumerId = ConfigurationManager.AppSettings.AllKeys.Contains("PCI.ConsumerID") && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["PCI.ConsumerID"]) ? ConfigurationManager.AppSettings["PCI.ConsumerID"] : "";
    var domainName = ConfigurationManager.AppSettings.AllKeys.Contains("PCI.DomainName") && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["PCI.DomainName"]) ? ConfigurationManager.AppSettings["PCI.DomainName"] : "";

    var currentUserContext = new UserContext();
    var currentUserProfile = currentUserContext.CurrentUserProfile;
    string username = currentUserProfile.UserName;

    username = username.ToLowerInvariant();

    if (SessionManager2.Session["CSRFToken-" + username] != null)
    {
        CSRFToken = SessionManager2.Session["CSRFToken-" + username].ToString();
    }
    else
    {
        CSRFToken = "Dummy_CSRF_token_Billing";
    }
    string localizationWebServiceBaseUrl = string.Empty;
    string uxpWebServiceBaseUrl = string.Empty;
    string acctNumber = string.Empty;
    string acctNumberEncrypted = string.Empty;
    acctNumberEncrypted = System.Web.HttpContext.Current.Request.QueryString["accountNumber"];
    acctNumberEncrypted = AntiXssEncoder.HtmlEncode(acctNumberEncrypted, false);
    acctNumber = EncryptionManager.DecryptValueWithDecodingIfEnabled(acctNumberEncrypted);
    string province = PageManager.CurrentPageContext.Page.Province.TwoCharacterName;

    #region Autopay changes
    bool autopay_Toggle = eCare.Common.Utilities.Utility.GetONOFFToggleSwitch(eCare.Common.Constants.ONOFFSwitchKeys.Autopay_Toggle);


    var smSessionCk = HttpContext.Current.Request.Cookies["SMSESSION"] != null ? HttpContext.Current.Request.Cookies["SMSESSION"].ToString() : string.Empty;

    List<string> allBanData = new List<string>();

    int banCount = Model.Items.Count;

    var allBans = eCare.Bell.UI.Home.Controllers.Profile.BillingServiceProfileController.GetAllBans();

    allBanData.AddRange(allBans);

    var custId = eCareControllerBase.CurrentBUP.CustomerId;

    PaymentUIManager.RemoveCache("selectedBans" + custId);

    List<GetIncentiveDiscountDetailsRequest> getIncentiveDiscountDetailsRequestList = new List<GetIncentiveDiscountDetailsRequest>();
    foreach (var ban in allBanData)
    {
        GetIncentiveDiscountDetailsRequest getIncentiveDiscountDetailsRequest = new GetIncentiveDiscountDetailsRequest();
        ImplGetIncentiveDiscountDetailsRestInput implGetIncentiveDiscountDetailsRestInput = new ImplGetIncentiveDiscountDetailsRestInput();
        BaseInput baseInput = new BaseInput();
        baseInput.requestedPaymentInformation = new List<RequestedPaymentInformation>();
        baseInput.requestedPaymentInformation.Add(new RequestedPaymentInformation() { requestedPaymentMethod = eCare.Common.Constants.AutopayKeys.DebitPaymentMethod});
        baseInput.requestedPaymentInformation.Add(new RequestedPaymentInformation() { requestedPaymentMethod = eCare.Common.Constants.AutopayKeys.CreditPaymentMethod});
        baseInput.requestedPaymentInformation.Add(new RequestedPaymentInformation() { requestedPaymentMethod = eCare.Common.Constants.AutopayKeys.CancelPaymentMethod});
        baseInput.ban = ban;
        baseInput.brand = ConfigurationManager.AppSettings[eCare.Common.Constants.WebConfigKeys.Brand];
        baseInput.compVerNum = 0;
        baseInput.impersonatedID = "";
        baseInput.language = Bell.ca.Framework.Common.PageContext.CurrentPageContext.Page.Language.CultureName.ToLower().ToString() ?? "en"; //
        baseInput.messageId = "";
        baseInput.salesChannel = ConfigurationManager.AppSettings[eCare.Common.Constants.SalesChannelConfigKey];
        baseInput.operatorId = ConfigurationManager.AppSettings[eCare.Common.Constants.OperatorIdConfigKey];

        DateTimeOffset dateTimeOffset = DateTimeOffset.Now;

        string timestamp = dateTimeOffset.ToString("yyyy-MM-ddTHH:mm:ss.fffzzz");

        baseInput.clientTimeStamp = timestamp;
        baseInput.messageId = timestamp;

        implGetIncentiveDiscountDetailsRestInput.baseInput = baseInput;
        getIncentiveDiscountDetailsRequest.ImplGetIncentiveDiscountDetailsRestInput = implGetIncentiveDiscountDetailsRestInput;
        getIncentiveDiscountDetailsRequestList.Add(getIncentiveDiscountDetailsRequest);
    }

    var setiings = new JsonSerializerSettings
    {
        NullValueHandling = NullValueHandling.Ignore,
        Formatting = Formatting.None
    };
    var getIncentiveDiscountDetailsRequestListJson = JsonConvert.SerializeObject(getIncentiveDiscountDetailsRequestList, setiings);
    bool ople_debit = true;
    bool ople_Credit = true;
    string paymentMethod = string.Empty;
    string topUp = string.Empty;

    if (System.Web.HttpContext.Current.Items["paymentmethod"] != null)
    {
        paymentMethod = System.Web.HttpContext.Current.Items["paymentmethod"].ToString();
    }
    if (System.Web.HttpContext.Current.Items["topupoptions"] != null)
    {
        topUp = System.Web.HttpContext.Current.Items["topupoptions"].ToString();
    }

    var AutopayAPIUrl = ConfigurationManager.AppSettings.AllKeys.Contains("AutopayPromotionIncentiveDetailsAPI") && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["AutopayPromotionIncentiveDetailsAPI"]) ? ConfigurationManager.AppSettings["AutopayPromotionIncentiveDetailsAPI"] : null;
    #endregion
    if (eCare.Bell.UI.Common.Helpers.BellPageManager.CurrentPageContext != null && eCare.Bell.UI.Common.Helpers.BellPageManager.CurrentPageContext.Page.Culture != null && !String.IsNullOrEmpty(eCare.Bell.UI.Common.Helpers.BellPageManager.CurrentPageContext.Page.Culture.TwoLetterISOLanguageName))
    {
        lang = eCare.Bell.UI.Common.Helpers.BellPageManager.CurrentPageContext.Page.Culture.TwoLetterISOLanguageName;
    }

    string pageTitle = Html.GetStringResource("PAD_PAC_SIGNUP_TITLE_STEP") + " - " + Html.GetStringResource("PAD_PAC_MANAGE_TITLE");
    string flowInitUrl = "/";

    uxpWebServiceBaseUrl = ConfigurationManager.AppSettings.AllKeys.Contains("UXP.WebService.BaseUrl") && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["UXP.WebService.BaseUrl"]) ? ConfigurationManager.AppSettings["UXP.WebService.BaseUrl"] : "";
    localizationWebServiceBaseUrl = ConfigurationManager.AppSettings.AllKeys.Contains("Localization.WebService.BaseUrl") && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["Localization.WebService.BaseUrl"]) ? ConfigurationManager.AppSettings["Localization.WebService.BaseUrl"] : "";

    string banId = acctNumber;
    string seqNoFromQueryStr = System.Web.HttpContext.Current.Request.QueryString["seqNo"];
    seqNoFromQueryStr = AntiXssEncoder.HtmlEncode(seqNoFromQueryStr, false);
    string seqNo = !string.IsNullOrEmpty(seqNoFromQueryStr) ? seqNoFromQueryStr : null;
    var TimeOutSeconds = ViewBag.TimeOutSeconds;
    var userProfileProvince = ViewBag.ProfileProvince;
    var paymentItems = (List<eCare.Bell.UI.Common.Models.PreAuthPaymentItem>)ViewBag.PaymentItems;
    var customerId = ViewBag.CustomerId;

    var paymentItemsArray = eCare.Bell.UI.Home.Areas.Payment.Controllers.PreAuthorizedSignUpController.GetGuidValues(ViewBag.PaymentItems);
    var bankList = Model.BankAccountInfo.BankList;
    var requestUrl = Request.Url.ToString().IndexOf("?") > 0 ? Request.Url.ToString().Split('?')[0] : Request.Url.ToString();
    var ErrorInformation = Html.GetStringResource("ERROR_FIELD_INFO");
    var IsInteracEnabled = eCare.Common.Utilities.Utility.IsManageInteracEnabled();
    var IsAutopayCreditEnabled = eCare.Common.Utilities.Utility.IsManageAutopayCreditEnabled();
    var AccountypePrefix = string.Empty;
    if (paymentItems != null && paymentItems.Count > 0 && banId != null)
    {
        var acctype = paymentItems.Find(item => EncryptionManager.DecryptValueWithDecodingIfEnabled(item.BanID) == banId);
        var acctprefix = acctype.AccountType;
        switch (acctprefix)
        {
            case PaymentItemAccountType.OneBill:
                AccountypePrefix = "B:";
                break;
            case PaymentItemAccountType.Mobility:
                AccountypePrefix = "M:";
                break;
            case PaymentItemAccountType.SingleBan:
                AccountypePrefix = "S:";
                break;
            default:
                break;
 
        }
    }
}


<!DOCTYPE html>
<html lang="@lang">
<head>
    <title>@pageTitle</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="~/Styles/BRF3/core/css/brui-simple-global-nav.min.css" rel="stylesheet">
    <link href="~/Styles/BRF3/core/css/brui-icons-simple-global-nav.min.css" rel="stylesheet">
    <link href="~/Styles/BRF3/core/css/bell-react-ui-library.css" rel="stylesheet">
    <link href="~/Styles/BRF3/content/css/bell-payment-flow-manage.css" rel="stylesheet">
    <!-- <link href="~/Styles/BRF3/content/css/bell-payment-flow.css" rel="stylesheet"> -->
    <!--For footer -->
    <!-- =================Omniture Script======================= -->
    <script type="text/javascript" src="/Resource/custom/js/WidgetOmniture.js"></script>


    <style>
        body {
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 100vh;
            background-color: #fff;
        }

        main {
            flex: 1 0 auto;
            overflow: hidden;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border-width: 0;
        }

        #terms-and-condition-content a {
            /* text-decoration: underline; */
            color: #00549A;
        }


            #terms-and-condition-content a:hover {
                /* text-decoration: none; */
                color: #003778;
            }

            #terms-and-condition-content a:focus {
                outline-offset: 2px;
                outline: 2px solid #00549a;
                border-radius: 6px;
            }
    </style>
</head>
<body aria-busy="false">
    <!-- =================BWTK frameworks======================= -->
    <script src="@Url.Content("/Styles/WidgetAssets/bwtk/v6/lib-loader" + (isDebug ? ".js" : ".min.js"))"></script>

    @Html.Partial("~/Areas/Common/Views/Shared/TailwindLayoutHeader.cshtml", new ViewDataDictionary { { "FlowName", Html.GetStringResource("PAD_PAC_MANAGE_TITLE") }, { Html.GetStringResource("PAD_PAC_SIGNUP_TITLE_STEP"), "1" }, { "backUrl", flowInitUrl }, { "backUrlSRName", Html.GetStringResource("BACK_TO_MY_BELL") }, { "backUrlName", Html.GetStringResource("BACK_CTA") } })
     <script>
        var s_oAPT = "";
        var s_oPTE = false;
         var s_oSS2 = "generic";
         var s_oClientID = "@AccountypePrefix@banId";
      </script>
    <main role="presentation">
        @*render widget bundle;*@
        <h2 class="sr-only" aria-labelledby="payment-manage-heading"></h2>
        <div id="container">

            @if (Model.SelectedUpdatePaymentMethod == eCare.Bell.UI.Common.PreAuthorizedPaymentMethod.Creditcard)
            {
                @Html.HiddenFor(m => Model.SelectedBanID)
                for (int i = 0; i < Model.Items.Count; i++)
                {
                    @Html.HiddenFor(m => m.Items[i].BanID)
                    @Html.HiddenFor(m => m.Items[i].CreditCardDetails.CreditCardType)
                    @Html.HiddenFor(m => m.Items[i].CreditCardDetails.CreditCardNumberMasked)
                    @Html.HiddenFor(m => m.Items[i].CreditCardDetails.ExpireYear)
                    @Html.HiddenFor(m => m.Items[i].CreditCardDetails.ExpireMonth)
                    @Html.HiddenFor(m => m.Items[i].CreditCardDetails.SecurityCodeMasked)
                }
            }
            else if (Model.SelectedUpdatePaymentMethod == eCare.Bell.UI.Common.PreAuthorizedPaymentMethod.Debit)
            {
                for (int i = 0; i < Model.Items.Count; i++)
                {
                    @Html.HiddenFor(m => m.Items[i].BankAccountDetails.CardHolder)
                    @Html.HiddenFor(m => m.Items[i].BankAccountDetails.BankName)
                    @Html.HiddenFor(m => m.Items[i].BankAccountDetails.TransitCode)
                    @Html.HiddenFor(m => m.Items[i].BankAccountDetails.BankCode)
                    @Html.HiddenFor(m => m.Items[i].BankAccountDetails.AccountNumberMasked)
                }
            }
        </div>

        @*<div id="spinner" class="loader-fixed bgWhite">
                <div class="floatL col015">
                    <div class="loading-indicator-spinner"></div>
                </div>
                <div id="LoaderText" class="sr-only" role="alert">
                    @Html.GetStringResource("LOADING_INDICATOR")
                </div>
            </div>*@
    </main>
    <script>
        //Autopay changes start
        var autoPaySubscribersPADtoPACC = null;
        var autoPaySubscribersPACCtoPAD = null;
        var removedSubscriberOffers = null;
       $(document).ready(function () {

         var autopayToggle = @autopay_Toggle.ToString().ToLower();

        if (autopayToggle == true) {

            if (@System.String.IsNullOrEmpty(AutopayAPIUrl).ToString().ToLower() == false) {
                var allBanRequestList = @Html.Raw(getIncentiveDiscountDetailsRequestListJson);
                function allBanRequestsCompleted() {
                    console.log("Partial View load")
                    $("#brfLoadingIndicatorOverlay").show();
                    var promises = [];
                    var successfullReponses = [];
                    allBanRequestList.forEach((requestBody) => {
                        var promise = $.ajax(
                            {
                                type: "POST",
                                url: "@AutopayAPIUrl",
                                xhrFields: {
                                    withCredentials: true
                                },
                                headers: {
                                    "accept-language": "en",
                                    "brand": "b",

                                    "applicationId": "MBM_IOS",
                                @{
                                    if (!System.String.IsNullOrEmpty(CSRFToken))
                                    {
                                        <text> "X-CSRF-TOKEN": "@CSRFToken",</text>
                                    }

                                }
                                    "cookie": "SMSESSION=" + "@smSessionCk"


                                },
                                data: JSON.stringify(requestBody),
                                dataType: "json",
                                contentType: "application/json",

                            }).then(function (response) {
                                successfullReponses.push(response);
                                return response;
                            })
                            .catch(function (error) {
                                throw error;
                            })
                        promises.push(promise);
                    })

                    Promise.allSettled(promises).then(function (responses) {
                        const fullfilledRes = responses.some(res => res.status === 'fulfilled');

                        if (fullfilledRes) {
                            GetIncentiveDiscountDetails(successfullReponses);
                        }
                        else {
                            GetIncentiveDiscountDetails([]);
                        }

                    }).catch(function (error) {

                        GetIncentiveDiscountDetails([

                        ]);
                    })
                   /* pageLoadOmniture();*/
                }

                allBanRequestsCompleted();
            }

            else {
                GetIncentiveDiscountDetails([]);
            }


            function GetIncentiveDiscountDetails(responseList) {
                var payload = {
                    PreAuthorizedPayment: @Html.Raw(Json.Encode((PreAuthorizedPayment)Model)),
                    GetIncentiveDiscountDetailsResponseList: responseList
                }
                        $.ajax(
                            {
                                type: "POST",
                                url: '/BillingServiceProfile/GetIncentiveDiscountDetailsResponse?accountNo='+'@ViewData["AccountNumber"]',
                                dataType: "html",
                                data: payload,
                                //contentType: "application/json",
                                success: function (result) {
                                    var returnedData = JSON.parse(result);
                                    autoPaySubscribersPADtoPACC = returnedData.AutopayOffersPACC;
                                    autoPaySubscribersPACCtoPAD = returnedData.AutopayOffersPAD;
                                    removedSubscriberOffers = returnedData.AutopayOffersRemoved;
                                    prepareConfig();
                                    loader.start(LoadFun);
                                },
                                error: function (xhr, status, error) {
                                    prepareConfig();
                                    loader.start(LoadFun);
                                },
                                complete: function (xhr, status) {

                                }
                            });
            }
        }
    });



        // Autopay changes end
        //function showeCareLoader() {
        //    try {
        //        $('#spinner').show();
        //    }
        //    catch (e) { }
        //}

        //function hideeCareLoader() {
        //    try {
        //        $('#spinner').hide();
        //    }
        //    catch (e) { }
        //}

        //Accss fixes
        var OtpFailure = "[ERR_OTP_404]"
        function getConfirmationMessage() {
            var ConfirmationPageMessage = "";
            var ConfirmationMessage = $("#ConfirmationSuccess").text();
            ConfirmationPageMessage += ConfirmationMessage.substr(0, 50).replaceAll("  ", "").replaceAll("\n", "").replaceAll("Success", "") + "..." + ":C";
            return ConfirmationPageMessage;
        }
        var BankNameError = "ERR_BANK";
        var AccountholdernameError = "ERR_ACC_HOLDR_NAME";
        var TransitnumberError = "ERR_TRANSIT";
        var AccountnumberError = "ERR_ACC_NO";
        var CreditcardnumberError = "ERR_CCNO";
        var CardholdernameError = "ERR_CCNAME";
        var ExpirationdateError = "ERR_CCEXP";
        var CardsecuritycodeError = "ERR_CCSECCD";
        var fieldInformation =  "@Html.GetStringResource("ERROR_FIELD_INFO")";
        function gets_oARS(errorcode) {
            var s_oars = "";
            if (errorcode == "BANKERROR") {
                if ($("#error-2").text() != "") {
                    s_oars += BankNameError +",";
                }
                if ($("#error-3").text() != "") {
                    s_oars += AccountholdernameError + ",";
                }
                if ($("#error-4").text() != "") {
                    s_oars += TransitnumberError + ",";
                }
                if ($("#error-5").text() != "") {
                    s_oars += AccountnumberError ;
                }
            }
            else {
                if ($("#error-2").text() != "") {
                    s_oars += CreditcardnumberError + ",";
                }
                if ($("#error-3").text() != "") {
                    s_oars += CardholdernameError + ",";
                }
                if ($("#error-4").text() != "") {
                    s_oars += ExpirationdateError + ",";
                }
                if ($("#error-5").text() != "") {
                    s_oars += CardsecuritycodeError ;
                }
            }
            return s_oars;
        }

        function gets_oERR_CLASS(errorcode) {

            var s_oERR_CLASS = "";
            if (errorcode == "BANKERROR") {
                if ($("#error-2").text() != "") {
                    s_oERR_CLASS += BankNameError +"[V|FE]" + ",";
                }
                if ($("#error-3").text() != "") {
                    s_oERR_CLASS += AccountholdernameError + "[V|FE]" + ",";
                }
                if ($("#error-4").text() != "") {
                    s_oERR_CLASS += TransitnumberError+"[V|FE]"+",";
                }
                if ($("#error-5").text() != "") {
                    s_oERR_CLASS += AccountnumberError + "[V|FE]" ;
                }
            }
            else {
                if ($("#error-2").text() != "") {
                    s_oERR_CLASS += CreditcardnumberError + "[V|FE]" + ",";
                }
                if ($("#error-3").text() != "") {
                    s_oERR_CLASS += CardholdernameError + "[V|FE]" + ",";
                }
                if ($("#error-4").text() != "") {
                    s_oERR_CLASS += ExpirationdateError + "[V|FE]" + ",";
                }
                if ($("#error-5").text() != "") {
                    s_oERR_CLASS += CardsecuritycodeError + "[V|FE]" ;
                }
            }
            return s_oERR_CLASS;
        }

        function gets_oERR_DESC(errorcode) {
            var s_oERR_DESC = "";
            if (errorcode == "BANKERROR") {
                if ($("#error-2").text() != "") {
                    s_oERR_DESC += BankNameError + ":" + fieldInformation + ",";
                }
                if ($("#error-3").text() != "") {
                    s_oERR_DESC += AccountholdernameError + ":" + fieldInformation + ",";
                }
                if ($("#error-4").text() != "") {
                    s_oERR_DESC += TransitnumberError + ":" + fieldInformation + ",";
                }
                if ($("#error-5").text() != "") {
                    s_oERR_DESC += AccountnumberError + ":" + fieldInformation ;
                }
            }
            else {
                if ($("#error-2").text() != "") {
                    s_oERR_DESC += CreditcardnumberError + ":" + fieldInformation + ",";
                }
                if ($("#error-3").text() != "") {
                    s_oERR_DESC += CardholdernameError + ":" + fieldInformation + ",";
                }
                if ($("#error-4").text() != "") {
                    s_oERR_DESC += ExpirationdateError + ":" + fieldInformation + ",";
                }
                if ($("#error-5").text() != "") {
                    s_oERR_DESC += CardsecuritycodeError + ":" + fieldInformation ;
                }
            }
            return s_oERR_DESC;
        }


        var InValidInputDetails = "[ERR_IPD_404]"
        function getPaymentMethodErrors(PaymentMethod) {
            var PaymentMethodPageMessage = "";
            var errormessage = $("#error-1").text()+ $("#error-2").text() + $("#error-3").text() + $("#error-4").text() + $("#error-5").text();
            var PaymentCreditDiscountMessage = PaymentMethod == "Bank payment" ? $("#Bank-discount-offer").text() : PaymentMethod == "Credit card" ? $("#Credit-discount-offer").text() : PaymentMethod == "Cancel payment" ? $("#Cancel-discount-offer").text() :"" ;
            var InteractSuccesMessage = $("#account-fetched-message").text();
            if (errormessage && errormessage != "") {
                PaymentMethodPageMessage += errormessage.length > 50 ? errormessage.substr(0, 50).replaceAll("  ", "").replaceAll("\n", "") + "..." + ":E:" + InValidInputDetails : errormessage.replaceAll("  ", "").replaceAll("\n", "") + ":E:" + InValidInputDetails;
            }
            if (PaymentCreditDiscountMessage && PaymentCreditDiscountMessage != "") {
                if (PaymentMethodPageMessage != "")
                    PaymentMethodPageMessage += PaymentCreditDiscountMessage.length > 50 ? "," + PaymentCreditDiscountMessage.substr(0, 50).replaceAll("  ", "").replaceAll("\n", "") + "..." + ":I" : "," + PaymentCreditDiscountMessage.replaceAll("  ", "").replaceAll("\n", "") + ":I";
                else {
                    PaymentMethodPageMessage += PaymentCreditDiscountMessage.length > 50 ? PaymentCreditDiscountMessage.substr(0, 50).replaceAll("  ", "").replaceAll("\n", "") + "..." + ":I" : PaymentCreditDiscountMessage.replaceAll("  ", "").replaceAll("\n", "") + ":I";
                }
            }
            if (InteractSuccesMessage && PaymentCreditDiscountMessage != "") {
                if (PaymentMethodPageMessage != "")
                    PaymentMethodPageMessage += InteractSuccesMessage.length > 50 ? "," + InteractSuccesMessage.substr(0, 50).replaceAll("  ", "").replaceAll("\n", "") + "..." + ":I" : "," + InteractSuccesMessage.replaceAll("  ", "").replaceAll("\n", "") + ":I"
                else
                    PaymentMethodPageMessage +=
                        InteractSuccesMessage = InteractSuccesMessage.length > 50 ? InteractSuccesMessage.substr(0, 50).replaceAll("  ", "").replaceAll("\n", "") + "..." + ":I" : InteractSuccesMessage.replaceAll("  ", "").replaceAll("\n", "") + ":I"
            }
            return PaymentMethodPageMessage;
        }
        function removeBodyAttr() {
            var widget = $(".bell-preauth-setup"),
                widgetNext = $(".bell-preauth-setup button.brui-rounded-30"),
                body = $("body");

            if (widget.length > 0) {
                widgetNext.on('click', function () {
                    body.removeAttr("aria-live");
                    body.removeAttr("aria-busy");
                });
            }
        }

        function firstFocus() {
            var backButton = $("#back-button");
                backButton.focus();
        }
        var PaymentItems = @Html.Raw(Json.Encode(paymentItems));
        var transactionIds = @Html.Raw(Json.Encode(paymentItemsArray));
        var BankList = @Html.Raw(Json.Encode(bankList));

        var urlPrefix = "/UXP.Services/eCare/Ordering/Mobility/";
        var province = "@PageManager.CurrentPageContext.Page.Province.TwoCharacterName";
        var DTSConf = @Html.Raw(Bell.ca.CustomerInfo.Facade.Managers.PaymentManager.GetDTSTokenizationWidgetConfig(consumerId, appId, "12345", "test", TimeOutSeconds * 1000));

        var links = {
            PAYMENT_ORDER_FORM: urlPrefix + 'Onebill/OrderForm/PreAuthorizePayment',
            MULTI_PAYMENT_ORDER_FORM: urlPrefix + 'Onebill/OrderMultiForm/PreAuthorizePayment',
            PAYMENT_API_URL: "/UXP.Services/ecare/Serviceaccount/Mobility/",
            REDIRECT_URL_API: "/UXP.Services/eCare/Payment/Prefill/BankInfoProviderUrl",
            INTERAC_BANK_INFO_URL: "/UXP.Services/eCare/Payment/Prefill/BankInfo",
            CANCEL_PREAUTH_API_URL: "/UXP.Services/eCare/Billing/BillingAccounts/Payment/Preauthorized"
        };

        var loader = new BwtkLoader();
        var bundle = "/Styles/Widgets/Payments/bell-preauth-manage/bell-preauth-manage-bundle-bundle@(isDebug == true ? ".js" : ".min.js" )";
        loader.start({ mode: "@(isDebug ? "development" : "production")", bundlePath: bundle }, (Bundle) => {            

        var config = null;
        function prepareConfig() {

            config = Object.assign({
                "localization.webServicesPath": "@localizationWebServiceBaseUrl" + "/UXP.Services/Common/localization/ResourceBundle?widget=",
                "Preauth/Manage/bell-preauth-manage/language": "@lang",
                "Preauth/Manage/bell-preauth-manage/brand": "@ConfigurationManager.AppSettings["Brand"]",
                "Preauth/Manage/bell-preauth-manage/userID": "@customerId",
                "Preauth/Manage/bell-preauth-manage/channel": "@System.Configuration.ConfigurationManager.AppSettings[eCare.Common.Constants.WebConfigKeys.ChannelType]",
                "Preauth/Manage/bell-preauth-manage/CSRFToken": "@CSRFToken",
                "Preauth/Manage/bell-preauth-manage/province": province,
                "Preauth/Manage/bell-preauth-manage/getPaymentItem": PaymentItems,
                "Preauth/Manage/bell-preauth-manage/createMultiPaymentURL": "@uxpWebServiceBaseUrl" + links.MULTI_PAYMENT_ORDER_FORM,
                "Preauth/Manage/bell-preauth-manage/pagetitle": "@pageTitle",
                "Preauth/Manage/bell-preauth-manage/DTSTokenization": DTSConf,
                "Preauth/Manage/bell-preauth-manage/paymentApiUrl": "@uxpWebServiceBaseUrl" + links.PAYMENT_API_URL,
                "Preauth/Manage/bell-preauth-manage/getBankList": BankList,
                "Preauth/Manage/bell-preauth-manage/transactionIdArray": transactionIds,
                "Preauth/Manage/bell-preauth-manage/RedirectUrl": "@uxpWebServiceBaseUrl" + links.REDIRECT_URL_API,
                "Preauth/Manage/bell-preauth-manage/BankInfoUrl": "@uxpWebServiceBaseUrl" + links.INTERAC_BANK_INFO_URL,
                "Preauth/Manage/bell-preauth-manage/currentUrl": "@requestUrl",
                "Preauth/Manage/bell-preauth-manage/selectedUpdatePaymentMethod": "@Model.SelectedUpdatePaymentMethod",
                "Preauth/Manage/bell-preauth-manage/debitCardAutopayOffers": autoPaySubscribersPACCtoPAD,
                "Preauth/Manage/bell-preauth-manage/creditCardAutopayOffers": autoPaySubscribersPADtoPACC,
                "Preauth/Manage/bell-preauth-manage/removedSubscriberOffers": removedSubscriberOffers,
                "Preauth/Manage/bell-preauth-manage/isCheckedBan": "@banId",
                "Preauth/Manage/bell-preauth-manage/CancelApiUrl": "@uxpWebServiceBaseUrl" + links.CANCEL_PREAUTH_API_URL,
                "Preauth/Manage/bell-preauth-manage/IsInteracEnabled": "@IsInteracEnabled",
                "Preauth/Manage/bell-preauth-manage/IsAutopayCreditEnabled": "@IsAutopayCreditEnabled",
                "Preauth/Manage/bell-preauth-manage/userProfileProvince": "@userProfileProvince"
            });
        }

    

    
        bundle.initialize(config, "container");
        var store = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Store);
        store.dispatch(bwtk.setLocale("@lang"));
        store.createGlobalActionListener(function (action) {
         /*   $("#container").trigger("widgetevent");*/
            //$("#container").attr("aria-hidden", "false");
            try {

                console.log(action.type);
                switch (action.type) {
                    case "WIDGET_STATUS":
                        console.log("Error:");
                        break;
                    case "TOKENIZATION_ERROR":
                         $('#back-button-tailwind').hide();
                            $('#bell-icon-tailwind').show();
                        break;
                    case "API_CONFIRMATION_STATUS":
                        if (action.payload.type === "FAILED" || action.payload.type === "COMPLETED") {
                            $('#back-button-tailwind').hide();
                            $('#bell-icon-tailwind').show();

                            $('#bell-icon-tailwind').addClass('bell-icon-tailwind-d-none-force');
                        } else {
                            $('#bell-icon-tailwind').removeClass('bell-icon-tailwind-d-none-force');
                        }
                        break;
                    case "OMNITURE_ON_LOAD":
                        typeof s_oTrackPage == "function" && s_oTrackPage({
                            s_oPGN: "Ban Select",
                            s_oSS3: "Change preauthorized payment",
                            s_oPLE: "",
                            s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                            s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")"
                        });
                        break;
                    case "OMNITURE_ON_PAYMENT_SELECT":
                        if (action.payload && action.payload != null) {
                            var s_oPLE = getPaymentMethodErrors(action.payload.data.s_oPYM);
                            var s_oERR_CLASS = gets_oERR_CLASS(action.payload.data.error) ;
                            var s_oERR_DESC = gets_oERR_DESC(action.payload.data.error) ;
                            var s_oARS = gets_oARS(action.payload.data.error);
                            var s_oAPT = action.payload.data.s_oAPT;
                            var s_oILI = action.payload.data.s_oILI;

                            if (action.payload.data.error == "BANKERROR" || action.payload.data.error == "CREDITERROR") {
                                typeof s_oTrackPage == "function" && s_oTrackPage({
                                    s_oPGN: "Paymentinformation",
                                    s_oSS3: "Change preauthorized payment",
                                    s_oAPT: s_oAPT,
                                    s_oERR_CLASS: s_oERR_CLASS,
                                    s_oERR_DESC: s_oERR_DESC,
                                    s_oARS: s_oARS,
                                    s_oPLE: s_oPLE,
                                    s_oILI: s_oILI,
                                    s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                                    s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",

                                });
                            }
                            else {

                                 typeof s_oTrackPage == "function" && s_oTrackPage({
                                    s_oPGN: "Paymentinformation",
                                    s_oSS3: "Change preauthorized payment",
                                    s_oAPT: s_oAPT,
                                    s_oPLE: s_oPLE,
                                    s_oILI: s_oILI,
                                    s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                                     s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",


                                });
                            }
                        }
                        break;
                     case "OMNITURE_ON_CANCELATION_COMPLETED":
                     var s_oPLE = "@Html.GetStringResource("SUCCESS_TOAST_MESSAGE")" +"..:C";
                     var s_oLBC = ""
                     typeof s_oTrackPage == "function" && s_oTrackPage({
                       s_oPGN: "Paymentinformation",
                       s_oAPT: "332-2-1",
                       s_oPLE: s_oPLE,
                       s_oPRM: "",
                       s_oLBC: "",
                         });
                       break;
                    case "OMNITURE_ON_REVIEW":
                               typeof s_oTrackPage == "function" && s_oTrackPage({
                                 s_oPGN: "Review",
                                 s_oSS3: "Change preauthorized payment",
                                 s_oPLE: "",
                                 s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                                 s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")"
                               });
                                break;
                    case "OMNITURE_ON_CONFIRMATION":
                        if (action.payload != null && action.payload.data != null) {
                             var s_oPYM = action.payload.data.s_oPYM != null ? action.payload.data.s_oPYM : "";
                            var s_oCCDT = action.payload.data.s_oCCDT != null ? action.payload.data.s_oCCDT.CreditCardType : "";
                            var s_oAPT = action.payload.data.s_oAPT != null ? action.payload.data.s_oAPT : "";
                            var s_oPID = action.payload.data.s_OPID != null ? action.payload.data.s_OPID : "";
                            var s_oPLE = $("#Confirmation-message").text().substr(0, 50).replace(":", "") + "...:C"
                            typeof s_oTrackPage == "function" && s_oTrackPage({
                                s_oPGN: "Confirmation",
                                s_oSS3: "Change preauthorized payment",
                                s_oAPT: s_oAPT,
                                s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                                s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                                s_oPLE: s_oPLE,
                                s_oPYM: s_oPYM,
                                s_oCCDT: s_oCCDT,
                                s_oPID: s_oPID,

                            });
                        }
                        else {
                            typeof s_oTrackPage == "function" && s_oTrackPage({
                               s_oPGN: " Confirmation",
                                s_oSS3: "Change preauthorized payment",
                                s_oAPT: "160-2-2",
                               s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                               s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                               s_oPLE: "",
                               s_oARS: "",
                               s_oERR_CLASS: "",
                               s_oERR_DESC: "",
                              });
                        }
                        break;
                    case "OMNITURE_ON_FIND_TRANSACTION_LIGHT_BOX":
                        var s_oPLE = $("#transit-and-account").text().substr(0, 50)+"...:I";
                        var s_oLBC = $("#transit-and-account").text().substr(0, 100);
                        var s_oPRM = "@Html.GetStringResource("TRANSIT_ACCOUNT_NUMBERS_TITLE")";
                         typeof s_oTrackPage == "function" && s_oTrackPage({
                             s_oPGN: "Paymentinformation",
                             s_oSS3: "Change preauthorized payment",
                             s_oAPT: "104-0-0",
                             s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                             s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                             s_oPLE: s_oPLE,
                             s_oPRM: s_oPRM,
                             s_oLBC: s_oLBC
                        });
                         break;
                    case "OMNITURE_ON_BOX_NAME_LIGHT_BOX":
                        var s_oPLE = $("#no-name-on-card").text().substr(0, 50)+"...:I";
                        var s_oLBC = $("#no-name-on-card").text().substr(0, 100);
                        var s_oPRM = "@Html.GetStringResource("MODAL_NO_NAME_TITLE")";
                       typeof s_oTrackPage == "function" && s_oTrackPage({
                           s_oPGN: "Paymentinformation",
                           s_oSS3: "Change preauthorized payment",
                           s_oAPT: "104-0-0",
                           s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                           s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                           s_oPLE: s_oPLE,
                           s_oPRM: s_oPRM,
                           s_oLBC: s_oLBC
                       });
                        break;
                    case "OMNITURE_ON_SECURITY_CODE_LIGHT_BOX":
                        var s_oPLE = $("#what-is-security-code").text().substr(0, 50)+"...:I";
                        var s_oLBC = $("#what-is-security-code").text().substr(0, 100);
                        var s_oPRM = "@Html.GetStringResource("MODAL_SECURITY_CODE_TITLE")";
                      typeof s_oTrackPage == "function" && s_oTrackPage({
                          s_oPGN: "Paymentinformation",
                          s_oSS3: "Change preauthorized payment",
                          s_oAPT: "104-0-0",
                          s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                          s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                          s_oPLE: s_oPLE,
                          s_oPRM: s_oPRM,
                          s_oLBC: s_oLBC
                        });
                     break;
                    case "OMNITURE_ON_CANCELATION_PARTIALLY_COMPLETED":
                     var s_oPLE = "@Html.GetStringResource("CANCELATION_PARTIALLY_SUCCESS")" +"..:C";
                     var s_oLBC = ""
                     typeof s_oTrackPage == "function" && s_oTrackPage({
                       s_oAPT: "332-2-1",
                       s_oPLE: s_oPLE,
                       s_oPRM: "",
                       s_oLBC: "",
                         });
                       break;
                    case "OMNITURE_ON_CANCELATION_PARTIALLY_FAILED":
                     var s_oPLE = "@Html.GetStringResource("CANCELATION_PARTIALLY_FAILED")" +"..:E:[ERR_CANCEL]";
                     var s_oLBC = ""
                     typeof s_oTrackPage == "function" && s_oTrackPage({
                        s_oPGN :"Paymentinformation",
                        s_oSS3 :"Change preauthorized payment",
                        s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                        s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                        s_oAPT: "332-2-2",
                       s_oPLE: s_oPLE,
                       s_oARS:"ERR_CANCEL",
                       s_oERR_CLASS : "ERR_CANCEL[T|BE]",
                        s_oERR_DESC :"ERR_CANCEL:Some Cancellation Failed"
                         });
                       break;
                    case "OMNITURE_ON_CANCELATION_FAILED":
                     var s_oPLE = "@Html.GetStringResource("CANCELATION_FAILED")" +"..:E:[ERR_CANCEL]";
                     var s_oLBC = ""
                     typeof s_oTrackPage == "function" && s_oTrackPage({
                        s_oPGN :"Paymentinformation",
                        s_oSS3 :"Change preauthorized payment",
                        s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                        s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                        s_oAPT: "332-2-2",
                       s_oPLE: s_oPLE,
                       s_oARS:"ERR_CANCEL",
                       s_oERR_CLASS : "ERR_CANCEL[T|BE]",
                        s_oERR_DESC :"ERR_API:Cancellation Failed"
                         });
                       break;
                    case "OMNITURE_ON_CONFIRMATION_FAILURE":
                    if(action.payload != null && action.payload.data != null)
                    {   var s_oAPT = action.payload.data.s_oAPT != null ? action.payload.data.s_oAPT : "";
                        var s_oPLE =  "@Html.GetStringResource("CANCELATION_FAILED")"+"...:E:[ERR_REGFAIL]";
                     typeof s_oTrackPage == "function" && s_oTrackPage({
                        s_oPGN :"Review",
                        s_oSS3 :"Change preauthorized payment",
                        s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                        s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                        s_oAPT: s_oAPT,
                        s_oPLE: s_oPLE,
                        s_oARS:"ERR_REGFAIL",
                        s_oERR_CLASS : "ERR_REGFAIL[T|BE]",
                        s_oERR_DESC :"ERR_REGFAIL:Preauth Regesitration Failure"
                         });
                    }
                       break;
                       case "OMNITURE_ON_VALIDATION_FAILURE":
                                if(action.payload != null && action.payload.data != null){
                                    var s_oAPT = action.payload.data.s_oAPT != null ? action.payload.data.s_oAPT : "";
                                    var s_oPLE =  "@Html.GetStringResource("CANCELATION_FAILED")"+"...:E:[ERR_VALIDATEFAIL]"
                                    typeof s_oTrackPage == "function" && s_oTrackPage({
                                        s_oPGN :"Paymentinformation",
                                        s_oSS3 :"Change preauthorized payment",
                                        s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                                        s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                                        s_oAPT: s_oAPT,
                                        s_oPLE: s_oPLE,
                                        s_oARS:"ERR_VALIDATEFAIL",
                                        s_oERR_CLASS : "ERR_VALIDATEFAIL[V|BE]",
                                        s_oERR_DESC :"ERR_REGFAIL:Preauth Validation Failure"
                                    });
                                }
                            break;

                             case "OMNITURE_ON_INTERAC_FAILURE":
                                if(action.payload != null ){
                                    var s_oAPT = action.payload.s_oAPT != null ? action.payload.s_oAPT : "";
                                    var s_oPLE = "@Html.GetStringResource("ALERT_ERROR_HEADING_INTERAC")"+"@Html.GetStringResource("ALERT_ERROR_HEADING_INTERAC_DESC")"+"...:E:[ERR_INTERAC_FAIL]";
                                    typeof s_oTrackPage == "function" && s_oTrackPage({
                                        s_oPGN :"Paymentinformation",
                                        s_oSS3 :"Change preauthorized payment",
                                        s_oESTD: "@DateTime.Now.ToString("yyyy-MM-dd")",
                                        s_oESTT: "@DateTime.Now.ToString("HH:mm:ss.fff")",
                                        s_oAPT: s_oAPT,
                                        s_oPLE: s_oPLE,
                                        s_oARS:"ERR_INTERAC_FAIL",
                                        s_oERR_CLASS : "ERR_INTERAC_FAIL[T|BE]",
                                        s_oERR_DESC :"ERR_INTERAC_FAIL:Interac Failure"
                                    });
                                }
                            break;
                       
                }
            } catch (err) {
                console.log("Error:", err);
            }

            setTimeout(function () {
                removeBodyAttr();
            }, 500);

            setTimeout(function () {
                firstFocus();
            }, 1500);

        });
    
       });      //loader.start(LoadFun);
    </script>
    @Html.Partial("~/Areas/Common/Views/Shared/_TailwindFooter.cshtml")

</body>
</html>