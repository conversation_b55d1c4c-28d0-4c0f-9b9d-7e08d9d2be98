﻿<?xml version="1.0" encoding="utf-8"?>
<Configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <settings>    
    <add key="EnableModemReboot" value="true" />

    <!--Localization API URL -->
    <add key="LocalizationApiUrl" value="/UXP.Services/Common/localization/ResourceBundle?widget=" />

    <!--BPI API URLs-->
    <add key="StartProcessApiUrlPath" value="/bpi/api/wi/v1/hydrate/startProcess" />
    <add key="NextStepApiUrlPath" value="/bpi/api/wi/v1/nextStep?key=" />
    <add key="FeedStatusApiUrlPath" value="/bpi/api/wi/v1/feedStatus/" />
    <add key="FeedStatusApiUrlFullPath" value="/bpi/api/wi/v1/feedStatus/{CID}/TaskflowNotification" />
    <add key="FeedStatusTSVUrlFullPath" value="/bpi/api/wi/v1/feedStatus/{CID}/SelfRepairMilestones" />

	  <!--Response Step GUID Mapping -->
    <add key="InitialNextStepBGuid" value="2025.cd358905-494a-4621-9e3f-c8427664bb0e" />
    <add key="FeedStatusBGuid" value="2025.c2e3356d-d082-486d-9ddd-a992ee79625c" />
    <add key="ModemStatusOnlineBGuid" value="2025.c4a0bda6-f302-44d8-ac23-d0517364a9ed" />
    <add key="ModemStatusOfflineBGuid" value="2025.a86d5427-426e-4a12-9b0b-7fe778e07dc0" />
    <add key="ModemStatusRejectedBGuid" value="2025.0fbc104b-d1bd-41a3-820b-975fa33bdfd2" />
    <add key="ConfirmationSuccessBGuid" value="2025.d799117f-a403-4497-bee4-eacee4513cc1" />
    <add key="ConfirmationFailedBGuid" value="2025.655bba50-644d-4e4f-b9b7-da8474ef6e77" />
    <add key="ConfirmationRejectedBGuid" value="2025.d33a93f8-ba47-4953-96ed-16c055c7c720" />
	  <add key="Bell.CustomerProfile.Api.GetUserProfile.Path" value="CustomerAccounts/{0}/CustomerProfile?province={1}" />
	  <add key="Bell.CustomerProfile.Api.Base" value="UXP.Services/ecare/CustomerProfile/" />

	  <!--Steps Status Codes Names -->
    <add key="Completed" value="COMPLETED" />
    <add key="Failed" value="FAILED" />
    <add key="Pending" value="PENDING" />

    <add key="TimerDelay" value="1000" />
    <add key="RetryAttempts" value="9" />

    <!-- API Payload Options - Start Process -->
    <add key="ProcessNamePayload" value="Modem Reboot" />
    <add key="ProcessAppAcronymPayload" value="BELLIT0" />
    <!-- API Payload Options - Reboot-->
    <add key="ConfirmationPayload" value="yes" />

    <!-- Modem Image URL -->
    <add key="homehub6_s0192601" value="/Resource/custom/img/HH6E_IMG1_EN.png" />
    <add key="homehub4_s0114601" value="/Resource/custom/img/HH4K_IMG1_EN.png" />
    <add key="homehub3_s20100a24101v031601" value="/Styles/internet/all_languages/all_regions/catalog_images/large/HH3K_lrg1.png" />
    <add key="homehub2_s20100a24101v013216" value="/Styles/internet/all_languages/all_regions/catalog_images/large/HOME_HUB_2000_BLK_34L_220X202.jpg" />
    <add key="homehub1_s20100a24101v013217" value="/Styles/internet/all_languages/all_regions/catalog_images/large/HOME_HUB_1000_WHT_34L_220X202.jpg" />
    <add key="connecti_s20100a24101v43304" value="/Styles/internet/all_languages/all_regions/catalog_images/large/ConnHub.jpg" />
    <add key="homenetw_s20100a24101v43303"  value ="/Styles/internet/all_languages/all_regions/catalog_images/large/2Wire.jpg" />
    <add key="wifi7_s0304009" value="/Resource/custom/img/WIFI7_IMG1_EN.png" />
    <add key="default_image"  value ="/Styles/internet/all_languages/all_regions/catalog_images/large/othermodems.png" />
    <add key="ModemImageUrls"  value='[{"key": "homehub_GENERIC", "value": ["/Styles/BRF3/content/img/self-repair/modemImages/generic/homehub_GENERIC_STEP1.png","/Styles/BRF3/content/img/self-repair/modemImages/generic/homehub_GENERIC_STEP2.png","/Styles/BRF3/content/img/self-repair/modemImages/generic/homehub_GENERIC_STEP3.png","/Styles/BRF3/content/img/self-repair/modemImages/generic/homehub_GENERIC_STEP4.png"]}, {"key": "homehub1_s20100a24101v013217", "value": ["/Styles/BRF3/content/img/self-repair/modemImages/homehub1/homehub1_s20100a24101v013217_STEP1.png","/Styles/BRF3/content/img/self-repair/modemImages/homehub1/homehub1_s20100a24101v013217_STEP2.png","/Styles/BRF3/content/img/self-repair/modemImages/homehub1/homehub1_s20100a24101v013217_STEP3.png","/Styles/BRF3/content/img/self-repair/modemImages/homehub1/homehub1_s20100a24101v013217_STEP4.png"]}, {"key": "homehub2_s20100a24101v013216", "value": ["/Styles/BRF3/content/img/self-repair/modemImages/homehub2/homehub2_s20100a24101v013216_STEP1.png","/Styles/BRF3/content/img/self-repair/modemImages/homehub2/homehub2_s20100a24101v013216_STEP2.png","/Styles/BRF3/content/img/self-repair/modemImages/homehub2/homehub2_s20100a24101v013216_STEP3.png","/Styles/BRF3/content/img/self-repair/modemImages/homehub2/homehub2_s20100a24101v013216_STEP4.png"]}, {"key": "homehub3_s20100a24101v031601", "value": ["/Styles/BRF3/content/img/self-repair/modemImages/homehub3/homehub3_s20100a24101v031601_STEP1.png","/Styles/BRF3/content/img/self-repair/modemImages/homehub3/homehub3_s20100a24101v031601_STEP2.png"]}]'/>
    <add key="TvReceiverImageUrls"  value='{"FourK": ["/Styles/BRF3/content/img/self-repair/tvReceiverImages/4k/tv_receiver_4k_step1.png","/Styles/BRF3/content/img/self-repair/tvReceiverImages/4k/tv_receiver_4k_step2.png","/Styles/BRF3/content/img/self-repair/tvReceiverImages/4k/tv_receiver_4k_step3.png"], "Generic": ["/Styles/BRF3/content/img/self-repair/tvReceiverImages/generic/tv_receiver_generic_step1.png","/Styles/BRF3/content/img/self-repair/tvReceiverImages/generic/tv_receiver_generic_step2.png","/Styles/BRF3/content/img/self-repair/tvReceiverImages/generic/tv_receiver_generic_step3.png"]}'/>
    <!-- SelfRepair section -->

    <!--Pilot user list file-->
    <add key = "SelfRepairPilotUsersPath" value="/Content/GlobalResources/SelfRepairPilotUserList.xml"/>

    <add key = "selfRepairSessionTimeOutInMintues" value = "15" />


    <add key="milestones" value='[{"name":"OUTAGE_CHECK","start":["SharpCustomerRepairRequestReceivedEvent"],"end":"AcutRetrieveTroubleTicketQueryResponse","errorMilestones":["MoiRetrieveInfoQuery","AcutAppendCommentCommand","AcutCloseTroubleTicketCommand","AcutFindTroubleTicketQuery","AcutRetrieveTroubleTicketQuery","AcutSubmitTroubleTicketCommand","AcutAppendCommentCommandResponse","AcutCloseTroubleTicketCommandResponse","AcutFindTroubleTicketQueryResponse","AcutSubmitTroubleTicketCommandResponse", "MoiRetrieveInfoQueryResponse", "SharpCustomerRepairRequestReceivedEvent"],"showModal":false,"correctiveActions":[]},{"name":"OUTSIDE_HARDWARE_CHECK","start":["AcutRetrieveTroubleTicketQueryResponse"],"end":"ModemDeviceStatusInfoQuery","errorMilestones":["AcutVoiceDiagnosticMilestoneEvent", "SharpDispatchRequiredCommand", "SharpDispatchRequiredCommandResponse", "SharpResponseDispatchRequiredEvent", "WebcareInitiatePortResetCommand", "WebcareInitiatePortResetCommandResponse", "AuditRepairMilestoneEvent","WebcareCorrectFecCommand","WebcareFixBrasCommand","WebcareInitiateAuditRepairCommand","WebcareInitiateServiceDiagnosticCommand","WebcareCorrectFecCommandResponse","WebcareFixBrasCommandResponse","WebcareInitiateAuditRepairCommandResponse","WebcareInitiateServiceDiagnosticCommandResponse","WebcareBuildVccCommand", "WebcareBuildVccCommandResponse","WebcareBuildVccMilestoneEvent", "WebcareServiceDiagnosticMilestoneEvent"],"showModal":true, "correctiveActions":["WebcareCorrectFecCommandResponse","WebcareFixBrasCommandResponse","WebcareInitiateAuditRepairCommandResponse","EmssmInitiateOntResetCommandResponse","WebcareBuildVccCommandResponse","WebcareBuildVccMilestoneEvent","AuditRepairMilestoneEvent","WebcareInitiatePortResetCommandResponse"]},{"name":"TV_RECEIVER_REBOOT","start":["MediaRoomInitiateStbRebootCommand"],"end":"MediaRoomMilestoneEvent","errorMilestones":["MediaRoomInitiateStbRebootCommand","MediaRoomRetrieveStbInfoQuery","MediaRoomInitiateStbRebootCommandResponse","MediaRoomRetrieveStbInfoQueryResponse"],"showModal":false,"correctiveActions":[]},{"name":"MODEM_REBOOT","start":["ModemDeviceStatusInfoQuery"],"end":"ModemRebootMilestoneEvent","errorMilestones":["ModemRebootInitiateDeviceRebootCommand","ModemDeviceStatusInfoQuery", "ModemDeviceStatusInfoQueryResponse", "ModemDeviceStatusMilestoneEvent","EmssmInitiateOntResetCommand", "ModemRebootInitiateDeviceRebootCommand", "ModemRebootRetrieveDeviceInfoQuery", "ModemRebootInitiateDeviceRebootCommandResponse", "ModemRebootRetrieveDeviceInfoQueryResponse", "EmssmInitiateOntResetCommandResponse"],"showModal":false,"correctiveActions":[]},{"name":"MODEM_SPEED_TEST","start":["WebcareInitiateSpeedTestCommand","WebcareInitiateOptimizeSpeedCommand","WebcareChangeSpeedProfileCommand"],"end":"WebcareSpeedTestMilestoneEvent","errorMilestones":["WebcareInitiateSpeedTestCommand", "OptimizeSpeedMilestoneEvent","WebcareChangeSpeedProfileCommand", "WebcareChangeSpeedProfileCommandResponse","WebcareInitiateSpeedTestCommandResponse", "WebcareFindDeviceConfigurationCommand", "WebcareFindDeviceConfigurationCommandResponse", "WebcareInitiateOptimizeSpeedCommand", "WebcareInitiateOptimizeSpeedCommandResponse"],"showModal":true,"correctiveActions":["OptimizeSpeedMilestoneEvent","WebcareChangeSpeedProfileCommandResponse","WebcareFindDeviceConfigurationCommandResponse","WebcareInitiateOptimizeSpeedCommandResponse"]},{"name":"PHONE_TEST","start":["SsomInitiateVoiceReProvisioningCommand"],"end":"SsomMilestoneEvent","errorMilestones":["SsomInitiateVoiceReProvisioningCommand","SsomInitiateVoiceReProvisioningCommandResponse"],"showModal":false,"correctiveActions":["SsomInitiateVoiceReProvisioningCommandResponse","SsomMilestoneEvent"]}]'/>

    <add key="ServiceSelectionNavigation" value='[{"key": "UNIQUE_ADDRESS", "value": [{"key": "1",  "value": "LandingPage"}, {"key": "2", "value": "ServiceBackup"}, {"key": "3", "value": "ContactList"}, {"key": "4", "value": "SummarySelection"}]},{"key" :"MULTIPLE_ADDRESS", "value": [{"key": "1",  "value": "LandingPage"}, {"key": "2", "value": "AddressList"}, {"key": "3", "value": "ServiceBackup"}, {"key": "4", "value": "ContactList"}, {"key": "5", "value": "SummarySelection"}]}]'/>
    <add key="ServiceSelectionNavigationFMO" value='[{"key": "UNIQUE_ADDRESS", "value": [{"key": "1",  "value": "LandingPage"}, {"key": "2", "value": "ContactList"}, {"key": "3", "value": "SummarySelection"}]},{"key" :"MULTIPLE_ADDRESS", "value": [{"key": "1",  "value": "LandingPage"}, {"key": "2", "value": "AddressList"}, {"key": "3", "value": "ContactList"}, {"key": "4", "value": "SummarySelection"}]}]'/>

    <add key="keyValuePairs" value='{"OUTAGE_CHECK":"OUTAGE_CHECK","OUTSIDE_HARDWARE_CHECK":"OUTSIDE_HARDWARE_CHECK","TV_RECEIVER_REBOOT":"TV_RECEIVER_REBOOT","MODEM_REBOOT":"MODEM_REBOOT","MODEM_SPEED_TEST":"MODEM_SPEED_TEST","PHONE_TEST":"PHONE_TEST","SharpCustomerRepairRequestLifecycleEvent":"SharpCustomerRepairRequestLifecycleEvent","ONLINE":"ONLINE","STBONLINE":"STBONLINE","COMPLETED":"COMPLETED","REPAIR_SUCCESS":"REPAIR_SUCCESS","SUCCESS":"SUCCESS","MediaRoomRetrieveStbInfoQueryResponse":"MediaRoomRetrieveStbInfoQueryResponse","FeedStatusApiUrl":"/SelfRepairMilestones","CheckConnectionUrl":"virtualrepair/checkConnection","UxpEcareBaseUrl":"/UXP.Services/ecare/Serviceaccount/","TV":"TV/","EquipmentServiceType":"/Equipment?serviceType=","Province":"province=","Internet":"Internet/","Profile":"/Profile","SpeedTestActionKey":"Modem Speed Test","DownloadSpeedKey":"modem_actualDownloadSpeed","UploadSpeedKey":"modem_actualUploadSpeed","LatencyKey":"modem_latency","CustomerInputIndicator":"TaskFlowInformationRequiredEvent","CatchAllMSTemplateCode":"0000","CatchAllKOTemplateCode":"7100","SPTimeout":"SP Timeout","TimeoutKOCode":"7101"}'/>


    <add key ="SUPPORT" value ='[{"key": "TRIAGE_INTERNET" ,"value" :[{"title":"WHOLE_HOME_TITLE", "desc":"WHOLE_HOME_DESCRIPTION","link":"WHOLE_HOME_LINK"},
		       {"title" :"HOME_HUB_TITLE", "desc":"HOME_HUB_DESCRIPTION", "link": "HOME_HUB_LINK" },
		       {"title":"CHANGE_WIFI_TITLE", "desc":"CHANGE_WIFI_DESCRIPTION", "link":"CHANGE_WIFI_LINK"}]},
			   
			   {"key": "TRIAGE_TV" ,"value" :[{"title":"CONNECT_RECEIVER_TITLE", "desc":"CONNECT_RECEIVER_DESCRIPTION", "link":"CONNECT_RECEIVER_LINK"},
                 {"title":"FIBETV_REMOTE_TITLE", "desc":"FIBETV_REMOTE_DESCRIPTION", "link":"FIBETV_REMOTE_LINK"},
                 {"title":"SATELLITE_SERVICE_TITLE", "desc":"SATELLITE_SERVICE_DESCRIPTION", "link":"SATELLITE_SERVICE_LINK"},
		 {"title":"CHANNELS_HELP_TITLE", "desc":"CHANNELS_HELP_DESCRIPTION", "link":"CHANNELS_HELP_LINK"},
		 {"title":"USE_AND_TROUBLESHOOT_TITLE", "desc":"USE_AND_TROUBLESHOOT_DESCRIPTION", "link":"USE_AND_TROUBLESHOOT_LINK"},
		 {"title": "PAY_PER_VIEW_TITLE", "desc":"PAY_PER_VIEW_DESCRIPTION", "link":"PAY_PER_VIEW_LINK"}]},
		 
		 {"key": "TRIAGE_HOME_PHONE" ,"value" :[{"title":"TROUBLESHOOTING_PROBLEMS_TITLE", "desc":"TROUBLESHOOTING_PROBLEMS_DESCRIPTION", "link":"TROUBLESHOOTING_PROBLEMS_LINK"},
                        {"title":"CALL_FEATURE_TITLE", "desc":"CALL_FEATURE_DESCRIPTION", "link":"CALL_FEATURE_LINK"},
		        {"title":"REPLACE_BATTERY_TITLE","desc":"REPLACE_BATTERY_DESCRIPTION","link":"REPLACE_BATTERY_LINK"}]}]'/>
    <!-- API Payload Options - Start Process -->
    <add key="RepairProcessNamePayload" value="Self Repair Flow" />
    <add key="RepairProcessAppAcronymPayload" value="SELFRPR" />
    <add key="VRepairProcessNamePayload" value="Virtual Repair Flow" />
	<add key="VRepairProcessAppAcronymPayload" value="SLFRPRWEB" />
	<add key="HPAffectedServiceName" value="VOICE_CFS" />
	<add key="INTAffectedServiceName" value="internetSubscription" />
    <add key="RepairTimerDelay" value="2000" />
    <!--<add key="RepairTimerDelay" value="20000" />-->
    <add key="RepairRetryAttempts" value="9" />

    <!-- Self repair image urls -->


    <add key="wifi_pods" value="/Styles/BRF3/content/img/self-repair/2big_gpods.png"/>
    <add key="modem" value="/Styles/BRF3/content/img/self-repair/Bell-home-hub.png"/>
    <add key="bitmap" value="/Styles/BRF3/content/img/self-repair/Bitmap.png"/>
	  <add key="preTestImageUrls" value='["/Styles/BRF3/content/img/self-repair/img_pre-test_setup_1.png", "/Styles/BRF3/content/img/self-repair/img_pre-test_setup_1-img_pre-test_setup_2.png", "/Styles/BRF3/content/img/self-repair/img_pre-test_setup_1-img_pre-test_setup_3.png", "/Styles/BRF3/content/img/self-repair/img_pre-test_setup_1-img_pre-test_setup_4.png"]'/>
    <add key="modem_reboot" value="/Styles/BRF3/content/img/self-repair/lightboxImages/Modem_Reboot.png"/>
    <add key="modem_speed_test" value="/Styles/BRF3/content/img/self-repair/lightboxImages/Modem_Speed_Test.png"/>
    <add key="outage_check" value="/Styles/BRF3/content/img/self-repair/lightboxImages/outage_check.png"/>
    <add key="outside_hardware_check" value="/Styles/BRF3/content/img/self-repair/lightboxImages/Outside_Hardware_Check.png"/>
    <add key="phone_test" value="/Styles/BRF3/content/img/self-repair/lightboxImages/Phone_test.png"/>
    <add key="tv_receiver_reboot" value="/Styles/BRF3/content/img/self-repair/lightboxImages/TV_Receiver_Reboot.png"/>
    <add key="success" value="/Styles/BRF3/content/img/self-repair/success.gif"/>
    <add key="error" value="/Styles/BRF3/content/img/self-repair/error.gif"/>
    <add key="pending" value="/Styles/BRF3/content/img/self-repair/pending.gif"/>
    
    <add key="VoltAPIRequestTimeoutSec" value="180000"/>
    <add key="bitmap" value="/Styles/BRF3/content/img/self-repair/Bitmap.png"/>
    
    <add key="ModemsEligibleForPods" value="homehub2_s20100a24101v013216,homehub3_s20100a24101v031601,homehub4_s0114601,homehub6_s0192601"/>
	  <add key="WirecareProductID" value="wirecare_s0159601"/>

    <!--AIML Project keys-->
	  <add key="CMSRoute" value="/UXP.Services/Tools/Utilities/DynamicScreensWeb?actionId=" />
	  <add key="CMSSummaryRoute" value="/UXP.Services/Tools/Utilities/DynamicScreensActionListWeb" />
    <add key="processNameVRAIML" value="Virtual Repair - AIML" />
	  <add key="processAppAcronymVRAIML" value="VRAIML" />
	  
  </settings>
  
</Configuration>