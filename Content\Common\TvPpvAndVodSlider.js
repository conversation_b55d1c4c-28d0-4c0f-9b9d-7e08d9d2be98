var PpvAndVodSliderSettings = {
    "dots": true,
    "autoplay": false,
    "infinite": true,
    "arrows": true,
    "accessibility": true,
    "centerMode": false,
    "variableWidth": false, // note that using variableWidth will mess up the calculation below
    "slidesToShow": 5,
    "slidesToScroll": 5,
    "slidesRequired": 5,
    "speed": 500,
    "touchThreshold": 30,
    "centerPadding": "100px",
    "responsive": [
        {
            "breakpoint": 640,
            "settings": {
                "slidesRequired": 2,
                "slidesToShow": 2,
                "slidesToScroll": 1,
                "centerPadding": "50px"
            }
        },
        {
            "breakpoint": 999,
            "settings": {
                "slidesRequired": 4,
                "slidesToShow": 4,
                "slidesToScroll": 4,
                "centerPadding": "90px"
            }
        }
    ]
};

// update focusables on init and slide change
PpvAndVodSliderSettings.init = PpvAndVodSliderSettings.afterChange = function () {
    setTimeout(function () {
        fnFixSliderFocusables('.bell-posters.slick-initialized');
    }, 0);
};

// update focusables on window resize
var reinitTimeout_PpvAndVodSliderSettings;
$(window).on('resize', function () {
    clearTimeout(reinitTimeout_PpvAndVodSliderSettings);
    reinitTimeout_PpvAndVodSliderSettings = setTimeout(function () {
        fnFixSliderFocusables('.bell-posters.slick-initialized');
    }, 100);
});

function fnFixSliderFocusables(sliderSelector) {
    // we need to determine if slide is visible on viewport. react-slick has a bug which causes slick-active to become inaccurate so we need to determine it through other means
    $(typeof sliderSelector === "string" ? sliderSelector : ".slick-initialized").each(function () {
        var $slider = $(this),
            slidesToShow,
            hasMatch = false,
            $slides,
            numHiddenSlides;

        // check corresponding settings for current breakpoint. we prioritize the first match since that's what react-slick does
        slidesToShow = PpvAndVodSliderSettings.slidesToShow;
        PpvAndVodSliderSettings.responsive.forEach(function (p) {
            if (!hasMatch && window.matchMedia("(max-width: " + p.breakpoint + "px)").matches) {
                hasMatch = true;
                slidesToShow = p.settings.slidesToShow;
            }
        });

        // check each slide if visible on viewport and update attributes accordingly. we are relying on styles because react-slick's attributes are inconsistent/unreliable
        $slides = $(this).find(".slick-slide");
        numHiddenSlides = Math.round(Math.abs($slider.find(".slick-track").offset().left - $slider.find(".slick-list").offset().left) / $slides.first().outerWidth());
        $slides.each(function () {
            var $slide = $(this),
                slideIndex = $slide.index();

            if (slideIndex >= numHiddenSlides && slideIndex < numHiddenSlides + slidesToShow) {
                $slide.attr("aria-hidden", "false");
                $slide.find("a, button, [tabindex]").attr("tabindex", "0");
            } else {
                $slide.attr("aria-hidden", "true");
                $slide.find("a, button, [tabindex]").attr("tabindex", "-1");
            }
        });

        // remove tabindex from elements without roles
        $slides.find("div[tabindex=0]:not([role])").addBack("div[tabindex]:not([role])").removeAttr("tabindex");
    });
}

var hasTwoImageDesktopOneImageMobileConfig = {
    "dots": false,
    "autoplay": false,
    "infinite": true,
    "arrows": true,
    "accessibility": true,
    "centerMode": true,
    "variableWidth": false,
    "swipeToSlide": true,
    "slidesToShow": 2,
    "slidesToScroll": 2,
    "slidesRequired": 3,
    "speed": 500,
    "centerPadding": "90px",
    "touchThreshold": 30,
    "responsive": [
        {
            "breakpoint": 767,
            "settings": {
                "slidesRequired": 3,
                "slidesToShow": 2,
                "slidesToScroll": 2,
                "touchThreshold": 30,
                "centerPadding": "50px"
            }
        },
        {
            "breakpoint": 567,
            "settings": {
                "slidesRequired": 2,
                "slidesToShow": 1,
                "slidesToScroll": 1,
                "touchThreshold": 15,
                "centerPadding": "50px"
            }
        }
    ]
};
var hasOneImageConfig = {
    "dots": false,
    "autoplay": false,
    "infinite": true,
    "arrows": true,
    "accessibility": true,
    "centerMode": true,
    "variableWidth": false,
    "swipeToSlide": true,
    "slidesToShow": 1,
    "slidesToScroll": 1,
    "slidesRequired": 2,
    "speed": 500,
    "touchThreshold": 15,
    "centerPadding": "50px",
    "responsive": [
        {
            "breakpoint": 767,
            "settings": {
                "slidesRequired": 2,
                "slidesToShow": 1,
                "slidesToScroll": 1,
                "touchThreshold": 15,
                "centerPadding": "50px"
            }
        }
    ]
};